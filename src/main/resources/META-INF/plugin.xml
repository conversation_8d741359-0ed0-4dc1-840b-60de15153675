<idea-plugin>
  <idea-version since-build="241" until-build="252.*" />
  <version>2.0.0</version>
  <id>com.baaon.augment_assistant_enhanced</id>
  <name>Augment Assistant Enhanced</name>
  <vendor email="<EMAIL>" url="https://www.baidu.com">WQP</vendor>
  <description><![CDATA[<p>Augment Assistant Enhanced - 增强版本</p>
        <p>这是一个独立的增强版本，与原版本可以共存。</p>
        <p>主要功能：</p>
        <ul>
            <li>✅ SessionId 自动管理和替换</li>
            <li>✅ 试用认证系统（验证码：1024，3天试用期）</li>
            <li>✅ 正式认证系统（已优化，任意验证码有效）</li>
            <li>✅ 与 Augment Code 插件无缝集成</li>
            <li>✅ 自动启动和生命周期管理</li>
            <li>✅ 简化的配置界面</li>
        </ul>
        <p><strong>注意：</strong>此版本已优化正式验证逻辑，提供更好的用户体验。</p>]]></description>
  <depends>com.intellij.modules.platform</depends>
  <depends>com.augmentcode</depends>
  <extensions defaultExtensionNs="com.intellij">
    <projectActivity implementation="com.baaon.v2.AugmentStartupActivity" />
    <applicationConfigurable parentId="tools" instance="com.baaon.v2.config.AugmentConfigurable" id="com.baaon.v2.config.AugmentConfigurableEnhanced" displayName="Augment Assistant Enhanced" />
  </extensions>
  <applicationListeners>
    <listener class="com.baaon.v2.AugmentSessionReplacerPlugin" topic="com.intellij.ide.AppLifecycleListener" />
  </applicationListeners>
</idea-plugin>
