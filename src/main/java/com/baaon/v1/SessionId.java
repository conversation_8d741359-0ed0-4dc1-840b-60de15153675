 package com.baaon.v1;

 import com.intellij.ide.util.PropertiesComponent;
 import com.intellij.openapi.application.PermanentInstallationID;
 import com.intellij.openapi.diagnostic.Logger;
 import java.util.UUID;
 import org.jetbrains.annotations.NotNull;

/**
 * SessionId管理类（V1版本） - Augment Assistant插件的会话标识管理组件
 *
 * 主要功能：
 * 1. 管理和维护Augment Code插件所需的SessionId
 * 2. 提供SessionId的获取、存储和重置功能
 * 3. 支持多种SessionId来源的优先级管理
 * 4. 确保SessionId的持久化和一致性
 *
 * SessionId获取策略：
 * 1. 优先使用用户配置的存储SessionId
 * 2. 其次使用IntelliJ IDEA的永久安装ID
 * 3. 最后生成新的随机UUID作为备选
 *
 * 使用场景：
 * - Augment Code插件的身份验证
 * - 用户会话状态的维护
 * - 插件间的通信标识
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 1.0.0
 */




































 public class SessionId
 {
   /** 日志记录器，用于记录SessionId相关的操作和状态变化 */
   private static final Logger LOG = Logger.getInstance(SessionId.class);

   /** SessionId类的单例实例，确保全局唯一性和状态一致性 */
   @NotNull
   public static final SessionId INSTANCE = new SessionId();
















   /** SessionId在IntelliJ IDEA配置中的存储键名，用于持久化SessionId */
   @NotNull
   private static final String SESSION_ID_KEY = "augment.session.id";
















   /**
    * 获取或生成SessionId（主要方法）
    *
    * 此方法是获取SessionId的核心实现，采用以下策略：
    * 1. 首先尝试从IntelliJ IDEA的配置中获取已存储的SessionId
    * 2. 如果没有找到有效的存储SessionId，则生成新的UUID并保存
    * 3. 确保返回的SessionId始终有效且非空
    *
    * 实现逻辑：
    * - 使用PropertiesComponent获取持久化的SessionId
    * - 验证SessionId的有效性（非空且非空白）
    * - 生成新SessionId时使用UUID.randomUUID()确保唯一性
    * - 自动保存新生成的SessionId以供后续使用
    *
    * @return 有效的SessionId字符串，保证非空
    */
   @NotNull
   public String c1() {
     // 记录SessionId获取操作的开始
     LOG.info("获取SessionId");

     // 获取IntelliJ IDEA的属性组件实例，用于读取持久化配置
     PropertiesComponent properties = PropertiesComponent.getInstance();

     // 尝试从配置中读取已存储的SessionId
     String storedSessionID = properties.getValue("augment.session.id");

     // 检查存储的SessionId是否有效（非空且非空白字符）
     if (storedSessionID != null && !isBlank(storedSessionID)) {
       return storedSessionID;
     }

     // 如果没有有效的存储SessionId，生成新的UUID作为SessionId
     String newSessionID = UUID.randomUUID().toString();

     // 将新生成的SessionId保存到配置中，确保持久化
     properties.setValue("augment.session.id", newSessionID);

     // 记录新SessionId的生成，便于调试和追踪
     LOG.info("生成新的SessionId: " + newSessionID);

     return newSessionID;
   }
























   /**
    * 重置SessionId（强制生成新的SessionId）
    *
    * 此方法用于强制重置当前的SessionId，无论是否已存在有效的SessionId。
    * 主要用于以下场景：
    * 1. 用户手动重置SessionId的需求
    * 2. SessionId出现问题需要重新生成
    * 3. 插件重新初始化时的SessionId刷新
    *
    * 实现特点：
    * - 直接生成新的UUID，不检查现有SessionId
    * - 立即保存到配置中，覆盖原有值
    * - 记录重置操作，便于问题追踪
    *
    * @return 新生成的SessionId字符串，保证非空且唯一
    */
   @NotNull
   public String c2() {
     // 生成新的UUID作为SessionId，确保全局唯一性
     String newSessionId = UUID.randomUUID().toString();

     // 直接保存新SessionId到配置中，覆盖任何现有值
     PropertiesComponent.getInstance().setValue("augment.session.id", newSessionId);

     // 记录SessionId重置操作，包含新生成的SessionId值
     LOG.info("重置SessionId: " + newSessionId);

     return newSessionId;
   }











   /**
    * 检查字符串是否为空白
    *
    * 此工具方法用于检查字符串是否为null、空字符串或仅包含空白字符。
    * 比标准的isEmpty()更严格，会忽略仅包含空格、制表符等空白字符的字符串。
    *
    * 检查逻辑：
    * 1. 首先检查字符串是否为null
    * 2. 然后检查去除前后空白字符后是否为空字符串
    *
    * @param str 要检查的字符串
    * @return 如果字符串为null、空或仅包含空白字符则返回true，否则返回false
    */
   private boolean isBlank(String str) {
     // 检查字符串是否为null
     if (str == null) {
       return true;
     }
     // 去除前后空白字符后检查是否为空字符串
     return str.trim().isEmpty();
   }























   /**
    * 获取SessionId（不自动生成新ID版本）
    *
    * 此方法提供了一个不会自动生成新SessionId的获取方式，采用以下优先级策略：
    * 1. 首先尝试获取用户存储的SessionId
    * 2. 如果没有存储的SessionId，则使用IntelliJ IDEA的永久安装ID
    * 3. 如果都没有找到，返回空字符串而不是生成新ID
    *
    * 使用场景：
    * - 检查是否已存在有效的SessionId
    * - 避免意外生成新SessionId的情况
    * - 获取现有SessionId用于验证或显示
    *
    * @return 现有的SessionId字符串，如果没有找到则返回空字符串
    */
   @NotNull
   public String c3() {
     // 首先尝试从配置中获取用户存储的SessionId
     String storedSessionID = PropertiesComponent.getInstance().getValue("augment.session.id");
     if (storedSessionID != null && !isBlank(storedSessionID)) {
       return storedSessionID;
     }

     // 如果没有存储的SessionId，尝试使用IntelliJ IDEA的永久安装ID
     String installationID = PermanentInstallationID.get();
     if (installationID != null && !isBlank(installationID)) {
       // 记录使用永久安装ID作为SessionId的情况
       LOG.info("使用PermanentInstallationID作为SessionId: " + installationID);
       return installationID;
     }

     // 如果都没有找到有效的ID，记录警告并返回空字符串
     LOG.warn("没有找到有效的SessionId或InstallationID");
     return "";
   }

























   /**
    * 检查是否存在有效的SessionId
    *
    * 此方法用于检查当前是否已存在有效的SessionId，而不会生成新的SessionId。
    * 检查顺序与c3()方法相同，但只返回布尔值表示是否存在。
    *
    * 检查策略：
    * 1. 首先检查用户存储的SessionId是否有效
    * 2. 如果没有，检查IntelliJ IDEA的永久安装ID是否可用
    * 3. 只要其中任何一个有效就返回true
    *
    * 使用场景：
    * - 在UI中显示SessionId状态
    * - 决定是否需要生成新的SessionId
    * - 验证插件的初始化状态
    *
    * @return 如果存在有效的SessionId则返回true，否则返回false
    */
   public boolean hasValidSessionId() {
     // 检查用户存储的SessionId是否有效
     String storedSessionID = PropertiesComponent.getInstance().getValue("augment.session.id");
     if (storedSessionID != null && !isBlank(storedSessionID)) {
       return true;
     }

     // 检查IntelliJ IDEA的永久安装ID是否可用
     String installationID = PermanentInstallationID.get();
     return (installationID != null && !isBlank(installationID));
   }




















   /**
    * 清除存储的SessionId
    *
    * 此方法用于从IntelliJ IDEA的配置中删除用户存储的SessionId。
    * 注意：此操作只会清除用户配置的SessionId，不会影响永久安装ID。
    *
    * 使用场景：
    * - 用户手动重置SessionId时
    * - 插件卸载或重新初始化时
    * - 解决SessionId相关问题时
    *
    * 操作效果：
    * - 清除后，getSessionId()方法将使用永久安装ID或生成新ID
    * - hasValidSessionId()的结果可能会改变
    * - 记录清除操作以便追踪
    */
   public void clearStoredSessionId() {
     // 从IntelliJ IDEA配置中删除存储的SessionId
     PropertiesComponent.getInstance().unsetValue("augment.session.id");

     // 记录清除操作，便于调试和问题追踪
     LOG.info("已清除存储的SessionId");
   }






















   /**
    * 获取SessionId的来源类型
    *
    * 此方法用于确定当前SessionId的来源，返回描述性的字符串。
    * 主要用于调试、日志记录和用户界面显示。
    *
    * 返回值说明：
    * - "PropertiesComponent": SessionId来自用户配置存储
    * - "PermanentInstallationID": SessionId来自IntelliJ IDEA永久安装ID
    * - "Generated": SessionId需要生成（当前没有有效的现有ID）
    *
    * 检查逻辑：
    * 1. 首先检查用户存储的SessionId
    * 2. 然后检查永久安装ID
    * 3. 如果都没有，表示需要生成新ID
    *
    * @return SessionId来源的描述字符串
    */
   @NotNull
   public String c4() {
     // 检查用户存储的SessionId是否存在
     String storedSessionID = PropertiesComponent.getInstance().getValue("augment.session.id");
     if (storedSessionID != null && !isBlank(storedSessionID)) {
       return "PropertiesComponent";
     }

     // 检查永久安装ID是否可用，决定返回相应的来源类型
     String installationID = PermanentInstallationID.get();
     return (installationID != null && !isBlank(installationID)) ? "PermanentInstallationID" : "Generated";
   }



























   /**
    * 获取SessionId的详细信息
    *
    * 此方法返回包含SessionId值和来源的格式化字符串，
    * 主要用于调试、日志记录和用户界面显示。
    *
    * 功能特点：
    * - 组合SessionId值和来源信息
    * - 提供统一的格式化输出
    * - 便于问题诊断和状态展示
    *
    * 输出格式：
    * "SessionID: [实际ID值] (Source: [来源类型])"
    *
    * 实现逻辑：
    * 1. 调用c1()获取实际的SessionId值
    * 2. 调用c4()获取SessionId的来源类型
    * 3. 格式化组合成可读的信息字符串
    *
    * @return 格式化的SessionId信息字符串，包含ID值和来源
    */
   @NotNull
   public String c5() {
     // 获取当前的SessionId值
     String sessionId = c1();

     // 获取SessionId的来源类型
     String source = c4();

     // 格式化组合SessionId信息，便于显示和调试
     return String.format("SessionID: %s (Source: %s)", new Object[] { sessionId, source });
   }
 }