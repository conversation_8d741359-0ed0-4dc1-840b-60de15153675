 package com.baaon.v1.config;
 
 import com.baaon.SecuritySHA1Util;
 import com.baaon.v1.SessionId;
 import com.baaon.v1.SessionIdReplacer;
 import com.intellij.ide.util.PropertiesComponent;
 import com.intellij.openapi.diagnostic.Logger;
 import com.intellij.openapi.ui.Messages;
 import com.intellij.ui.components.JBLabel;
 import com.intellij.ui.components.JBTextField;
 import com.intellij.util.ui.FormBuilder;
 import com.intellij.util.ui.JBUI;
 import java.awt.Color;
 import java.awt.Component;
 import java.awt.FlowLayout;
 import java.awt.Font;
 import java.awt.Toolkit;
 import java.awt.datatransfer.ClipboardOwner;
 import java.awt.datatransfer.StringSelection;
 import java.awt.event.ActionEvent;
 import java.awt.event.ActionListener;
 import java.io.BufferedReader;
 import java.io.InputStreamReader;
 import java.net.HttpURLConnection;
 import java.net.URL;
 import java.net.URLEncoder;
 import java.nio.charset.StandardCharsets;
 import javax.swing.JButton;
 import javax.swing.JComponent;
 import javax.swing.JPanel;
 import javax.swing.SwingUtilities;
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 public class AugmentConfigPanel
 {
   private static final Logger LOG = Logger.getInstance(AugmentConfigPanel.class);
 
 
 
   
   private JPanel mainPanel;
 
 
 
   
   private JBTextField sessionIdField;
 
 
 
   
   private JBLabel sessionIdSourceLabel;
 
 
 
   
   private JBTextField verificationCodeField;
 
 
 
   
   private JButton verifyButton;
 
 
 
   
   private JBLabel verificationStatusLabel;
 
 
 
   
   private JButton generateButton;
 
 
 
   
   private JButton resetButton;
 
 
   
   private JButton copyButton;
 
 
   
   private String originalSessionId;
 
 
   
   private boolean modified = false;
 
 
   
   private boolean isVerified = false;
 
 
   
   private static final String VERIFICATION_API_URL = "https://baaon.com/api/aug/verify";
 
 
   
   private static final String VERIFICATION_STATUS_KEY = "augment.verification.status";
 
 
 
   
   public AugmentConfigPanel() {
     initializeComponents();
     setupLayout();
     setupEventHandlers();
     loadVerificationStatus();
     loadCurrentSettings();
   }
 
 
 
 
 
 
 
 
 
 
 
 
 
 
   
   private void initializeComponents() {
     this.verificationCodeField = new JBTextField();
     this.verificationCodeField.setColumns(10);
 
     
     this.verifyButton = new JButton("验证");
 
     
     this.verificationStatusLabel = new JBLabel("请输入验证码以启用SessionId功能");
     this.verificationStatusLabel.setFont(this.verificationStatusLabel.getFont().deriveFont(2));
     this.verificationStatusLabel.setForeground(Color.ORANGE);
 
     
     this.sessionIdField = new JBTextField();
     this.sessionIdField.setEditable(false);
     this.sessionIdField.setFont(new Font("Monospaced", 0, 12));
 
     
     this.sessionIdSourceLabel = new JBLabel();
     this.sessionIdSourceLabel.setFont(this.sessionIdSourceLabel.getFont().deriveFont(2));
 
     
     this.generateButton = new JButton("生成新的SessionId");
     this.resetButton = new JButton("重置SessionId");
     this.copyButton = new JButton("复制到剪贴板");
 
     
     updateButtonStates();
   }
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
   
   private void setupLayout() {
     JPanel verificationPanel = new JPanel(new FlowLayout(0, 5, 0));
     verificationPanel.add((Component)this.verificationCodeField);
     verificationPanel.add(this.verifyButton);
 
     
     JPanel buttonPanel = new JPanel(new FlowLayout(0, 5, 0));
     buttonPanel.add(this.generateButton);
     buttonPanel.add(this.resetButton);
     buttonPanel.add(this.copyButton);
 
     
     this
 
 
 
 
 
 
       
       .mainPanel = FormBuilder.createFormBuilder().addLabeledComponent("验证码:", verificationPanel, 1, false).addComponentToRightColumn((JComponent)this.verificationStatusLabel, 1).addSeparator().addLabeledComponent("当前SessionId:", (JComponent)this.sessionIdField, 1, false).addComponentToRightColumn((JComponent)this.sessionIdSourceLabel, 1).addComponent(buttonPanel, 1).addComponentFillVertically(new JPanel(), 0).getPanel();
 
     
     this.mainPanel.setBorder(JBUI.Borders.empty(10));
   }
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
   
   private void setupEventHandlers() {
     this.verifyButton.addActionListener(new ActionListener() {
           public void actionPerformed(ActionEvent e) {
             AugmentConfigPanel.this.verifyCode();
           }
         });
 
     
     this.verificationCodeField.addActionListener(new ActionListener() {
           public void actionPerformed(ActionEvent e) {
             AugmentConfigPanel.this.verifyCode();
           }
         });
 
     
     this.generateButton.addActionListener(new ActionListener() {
           public void actionPerformed(ActionEvent e) {
             if (AugmentConfigPanel.this.isVerified) {
               AugmentConfigPanel.this.generateNewSessionId();
             } else {
               AugmentConfigPanel.this.showVerificationRequiredMessage();
             } 
           }
         });
 
     
     this.resetButton.addActionListener(new ActionListener() {
           public void actionPerformed(ActionEvent e) {
             if (AugmentConfigPanel.this.isVerified) {
               AugmentConfigPanel.this.resetSessionId();
             } else {
               AugmentConfigPanel.this.showVerificationRequiredMessage();
             } 
           }
         });
 
     
     this.copyButton.addActionListener(new ActionListener() {
           public void actionPerformed(ActionEvent e) {
             if (AugmentConfigPanel.this.isVerified) {
               AugmentConfigPanel.this.copySessionIdToClipboard();
             } else {
               AugmentConfigPanel.this.showVerificationRequiredMessage();
             } 
           }
         });
   }
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
   
   private void loadCurrentSettings() {
     try {
       SessionId sessionIdInstance = SessionId.INSTANCE;
 
       
       String currentSessionId = sessionIdInstance.c1();
       String source = sessionIdInstance.c4();
 
       
       this.sessionIdField.setText(currentSessionId);
       this.sessionIdSourceLabel.setText("来源: " + getSourceDescription(source));
 
       
       this.originalSessionId = currentSessionId;
       this.modified = false;
       
       LOG.info("加载当前SessionId配置: " + currentSessionId + " (来源: " + source + ")");
     } catch (Exception e) {
       
       LOG.error("加载SessionId配置失败", e);
       this.sessionIdField.setText("加载失败");
       this.sessionIdSourceLabel.setText("来源: 未知");
     } 
   }
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
   
   private String getSourceDescription(String source) {
     switch (source) {
       case "PermanentInstallationID":
         return "永久安装ID";
       
       case "PropertiesComponent":
         return "已保存的配置";
       
       case "Generated":
         return "自动生成";
     } 
     
     return source;
   }
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
   
   private void generateNewSessionId() {
     try {
       String newSessionId = SessionId.INSTANCE.c2();
 
       
       this.sessionIdField.setText(newSessionId);
       this.sessionIdSourceLabel.setText("来源: " + getSourceDescription("PropertiesComponent"));
       this.modified = true;
       
       LOG.info("生成新的SessionId: " + newSessionId);
 
       
       try {
         SessionIdReplacer replacer = new SessionIdReplacer();
         boolean success = replacer.a2();
         
         if (success) {
           
           Messages.showInfoMessage("新的SessionId已生成并立即生效！\n\n" + newSessionId, "SessionId生成成功");
           LOG.info("SessionId替换成功，新SessionId已生效");
         } else {
           
           Messages.showWarningDialog("新的SessionId已生成并保存，但替换失败。\n\nSessionId: " + newSessionId + "\n\n请重启IDE以确保新SessionId生效。", "SessionId生成成功，但替换失败");
           LOG.warn("SessionId生成成功，但替换失败");
         } 
       } catch (Exception replaceException) {
         
         LOG.error("调用SessionIdReplacer失败", replaceException);
         Messages.showWarningDialog("新的SessionId已生成并保存，但替换过程出现异常。\n\nSessionId: " + newSessionId + "\n\n请重启IDE以确保新SessionId生效。\n\n错误详情: " + replaceException.getMessage(), "SessionId生成成功，但替换异常");
       } 
     } catch (Exception e) {
       
       LOG.error("生成SessionId失败", e);
       Messages.showErrorDialog("生成SessionId失败: " + e.getMessage(), "错误");
     } 
   }
 
 
 
 
 
 
 
   
   private void resetSessionId() {
     int result = Messages.showYesNoDialog("确定要重置SessionId吗？\n\n这将清除当前保存的SessionId，系统将使用永久安装ID或重新生成。", "确认重置", Messages.getQuestionIcon());
     if (result == 0) {
       
       try {
         SessionId.INSTANCE.clearStoredSessionId();
         loadCurrentSettings();
         this.modified = true;
         LOG.info("SessionId已重置");
 
         
         try {
           SessionIdReplacer replacer = new SessionIdReplacer();
           boolean success = replacer.a2();
           if (success) {
             Messages.showInfoMessage("SessionId已重置并立即生效！\n\n当前SessionId: " + this.sessionIdField.getText(), "重置成功");
             LOG.info("SessionId重置成功，新SessionId已生效");
           } else {
             Messages.showWarningDialog("SessionId已重置，但替换失败。\n\n当前SessionId: " + this.sessionIdField.getText() + "\n\n请重启IDE以确保重置后的SessionId生效。", "重置成功，但替换失败");
             LOG.warn("SessionId重置成功，但替换失败");
           } 
         } catch (Exception replaceException) {
           LOG.error("调用SessionIdReplacer失败", replaceException);
           Messages.showWarningDialog("SessionId已重置，但替换过程出现异常。\n\n当前SessionId: " + this.sessionIdField.getText() + "\n\n请重启IDE以确保重置后的SessionId生效。\n\n错误详情: " + replaceException.getMessage(), "重置成功，但替换异常");
         } 
       } catch (Exception e) {
         LOG.error("重置SessionId失败", e);
         Messages.showErrorDialog("重置SessionId失败: " + e.getMessage(), "错误");
       } 
     }
   }
 
 
 
 
 
   
   private void copySessionIdToClipboard() {
     try {
       String sessionId = this.sessionIdField.getText();
       if (sessionId != null && !sessionId.trim().isEmpty()) {
         
         Toolkit.getDefaultToolkit().getSystemClipboard().setContents(new StringSelection(sessionId), (ClipboardOwner)null);
         Messages.showInfoMessage("SessionId已复制到剪贴板！", "复制成功");
         LOG.info("SessionId已复制到剪贴板");
       } 
     } catch (Exception e) {
       LOG.error("复制SessionId失败", e);
       Messages.showErrorDialog("复制SessionId失败: " + e.getMessage(), "错误");
     } 
   }
 
 
 
 
 
 
 
   
   public JPanel getMainPanel() {
     return this.mainPanel;
   }
 
 
 
 
 
 
 
   
   public boolean isModified() {
     return this.modified;
   }
 
 
 
 
 
   
   public void apply() {
     this.modified = false;
     this.originalSessionId = this.sessionIdField.getText();
     LOG.info("配置已应用");
   }
 
 
 
 
 
   
   public void reset() {
     loadCurrentSettings();
     LOG.info("配置已重置");
   }
 
 
 
 
 
 
   
   private void verifyCode() {
     String inputCode = this.verificationCodeField.getText().trim();
     if (inputCode.isEmpty()) {
       Messages.showWarningDialog("请输入验证码！", "验证码为空");
     } else {
       
       this.verifyButton.setEnabled(false);
       this.verifyButton.setText("验证中...");
       this.verificationStatusLabel.setText("正在验证验证码，请稍候...");
       this.verificationStatusLabel.setForeground(Color.BLUE);
 
       
       SwingUtilities.invokeLater(() -> (new Thread(())).start());
     } 
   }
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
   
   private void showVerificationRequiredMessage() {
     Messages.showWarningDialog("请先输入正确的验证码以启用SessionId功能！", "需要验证");
   }
 
 
 
 
 
 
   
   private void updateButtonStates() {
     this.generateButton.setEnabled(this.isVerified);
     this.resetButton.setEnabled(this.isVerified);
     this.copyButton.setEnabled(this.isVerified);
 
     
     if (this.isVerified) {
       this.generateButton.setToolTipText("生成新的SessionId");
       this.resetButton.setToolTipText("重置SessionId");
       this.copyButton.setToolTipText("复制SessionId到剪贴板");
     } else {
       this.generateButton.setToolTipText("请先验证验证码");
       this.resetButton.setToolTipText("请先验证验证码");
       this.copyButton.setToolTipText("请先验证验证码");
     } 
   }
 
 
 
 
 
 
 
 
 
   
   private boolean callVerificationAPI(String code) throws Exception {
     boolean result;
     if (code.equals("error")) {
       return false;
     }
     long time = System.currentTimeMillis();
     String encode = SecuritySHA1Util.shaEncode("" + time + "10f2f96d89a32941edf01bxcz0a076f10");
     String urlString = "https://baaon.com/api/aug/verify?ts=" + time + "&sign=" + encode + "&code=" + URLEncoder.encode(code, StandardCharsets.UTF_8);
     LOG.info("调用验证API: " + urlString);
     URL url = new URL(urlString);
     HttpURLConnection connection = (HttpURLConnection)url.openConnection();
 
 
     
     try {
       connection.setRequestMethod("GET");
       connection.setConnectTimeout(10000);
       connection.setReadTimeout(10000);
       connection.setRequestProperty("Accept", "application/json");
       connection.setRequestProperty("User-Agent", "AugmentAssistant/1.0");
 
       
       int responseCode = connection.getResponseCode();
       LOG.info("API响应码: " + responseCode);
       if (responseCode != 200) {
         throw new Exception("API请求失败，响应码: " + responseCode);
       }
 
       
       BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8));
       StringBuilder response = new StringBuilder();
       
       String line;
       while ((line = reader.readLine()) != null) {
         response.append(line);
       }
       
       reader.close();
       String responseBody = response.toString();
       LOG.info("API响应内容: " + responseBody);
 
       
       boolean apiResult = (responseBody.contains("\"data\":true") || responseBody.contains("\"data\": true"));
       LOG.info("验证结果: " + apiResult);
       result = apiResult;
     } finally {
       connection.disconnect();
     } 
     
     return result;
   }
 
 
 
 
 
 
   
   private void loadVerificationStatus() {
     PropertiesComponent properties = PropertiesComponent.getInstance();
     boolean savedVerificationStatus = properties.getBoolean("augment.verification.status", false);
     if (savedVerificationStatus) {
       this.isVerified = true;
       this.verificationStatusLabel.setText("已验证！SessionId功能已启用");
       this.verificationStatusLabel.setForeground(Color.GREEN);
       this.verificationCodeField.setText("已验证");
       this.verificationCodeField.setEnabled(false);
       this.verifyButton.setEnabled(false);
       this.verifyButton.setText("已验证");
       updateButtonStates();
       LOG.info("加载已保存的验证状态：已验证");
     } else {
       this.isVerified = false;
       this.verificationStatusLabel.setText("请输入验证码以启用SessionId功能");
       this.verificationStatusLabel.setForeground(Color.ORANGE);
       updateButtonStates();
       LOG.info("加载已保存的验证状态：未验证");
     } 
   }
 
 
 
 
 
 
 
 
   
   private void saveVerificationStatus(boolean verified) {
     PropertiesComponent properties = PropertiesComponent.getInstance();
     properties.setValue("augment.verification.status", verified);
     LOG.info("保存验证状态: " + verified);
   }
 
 
 
 
 
 
   
   public void clearVerificationStatus() {
     PropertiesComponent properties = PropertiesComponent.getInstance();
     properties.unsetValue("augment.verification.status");
     this.isVerified = false;
     this.verificationCodeField.setText("");
     this.verificationCodeField.setEnabled(true);
     this.verifyButton.setEnabled(true);
     this.verifyButton.setText("验证");
     this.verificationStatusLabel.setText("请输入验证码以启用SessionId功能");
     this.verificationStatusLabel.setForeground(Color.ORANGE);
     updateButtonStates();
     LOG.info("已清除验证状态");
   }
 }