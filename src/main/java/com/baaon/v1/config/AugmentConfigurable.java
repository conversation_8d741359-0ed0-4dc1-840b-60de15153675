 package com.baaon.v1.config;
 
 import com.intellij.openapi.options.Configurable;
 import com.intellij.openapi.options.ConfigurationException;
 import com.intellij.openapi.util.NlsContexts.ConfigurableName;
 import javax.swing.JComponent;
 import org.jetbrains.annotations.Nls;
 import org.jetbrains.annotations.Nullable;
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 public class AugmentConfigurable
   implements Configurable
 {
   private AugmentConfigPanel configPanel;
   
   @Nls(capitalization = Nls.Capitalization.Title)
   @ConfigurableName
   public String getDisplayName() {
     return "Augment Assistant";
   }
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
   
   @Nullable
   public JComponent createComponent() {
     if (this.configPanel == null) {
       this.configPanel = new AugmentConfigPanel();
     }
 
     
     return this.configPanel.getMainPanel();
   }
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
   
   public boolean isModified() {
     return (this.configPanel != null && this.configPanel.isModified());
   }
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
   
   public void apply() throws ConfigurationException {
     if (this.configPanel != null) {
       this.configPanel.apply();
     }
   }
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
   
   public void reset() {
     if (this.configPanel != null) {
       this.configPanel.reset();
     }
   }
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
   
   public void disposeUIResources() {
     this.configPanel = null;
   }
 }