 package com.baaon.v1;

 import com.intellij.openapi.application.Application;
 import com.intellij.openapi.application.ApplicationManager;
 import com.intellij.openapi.diagnostic.Logger;
 import java.lang.reflect.Field;
 import java.util.Map;

/**
 * 不安全的跨插件访问工具类（V1版本） - 高级反射操作和插件间通信
 *
 * 警告：此类包含高风险的反射操作，可能导致系统不稳定
 *
 * 主要功能：
 * 1. 通过反射访问IntelliJ IDEA内部的服务注册表
 * 2. 动态查找和操作其他插件的服务实例
 * 3. 提供跨插件的深度集成能力
 * 4. 支持运行时的插件状态检查和修改
 *
 * 技术实现：
 * - 使用反射访问ApplicationManager的内部字段
 * - 操作服务注册表（myServices字段）
 * - 动态查找目标插件的服务实例
 * - 支持服务实例的替换和修改
 *
 * 安全风险：
 * - 直接操作IntelliJ IDEA的内部实现
 * - 可能因IDE版本更新而失效
 * - 反射操作可能导致安全异常
 * - 不当使用可能影响IDE稳定性
 *
 * 使用场景：
 * - 深度的插件间集成需求
 * - 无法通过标准API实现的功能
 * - 调试和问题排查
 * - 高级的插件开发需求
 *
 * 注意事项：
 * - 仅在必要时使用，优先考虑标准API
 * - 充分测试以确保兼容性
 * - 做好异常处理和降级方案
 * - 关注IDE版本兼容性
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 1.0.0
 */




























































 public class UnsafeCrossPluginAccess
 {
   /** 日志记录器，用于记录跨插件访问操作的详细过程和潜在风险 */
   private static final Logger LOG = Logger.getInstance(UnsafeCrossPluginAccess.class);























































































   /**
    * 通过反射获取指定插件的服务实例（危险操作）
    *
    * 警告：此方法使用高风险的反射操作访问IntelliJ IDEA的内部实现
    *
    * 功能说明：
    * 1. 通过反射访问ApplicationManager的内部服务注册表
    * 2. 遍历所有已注册的服务实例
    * 3. 根据服务类名查找匹配的服务实例
    * 4. 返回找到的服务实例或null
    *
    * 技术实现：
    * - 获取Application实例的myServices字段
    * - 设置字段为可访问状态（绕过private限制）
    * - 将字段值转换为Map类型
    * - 遍历Map中的所有服务实例
    * - 通过类名匹配查找目标服务
    *
    * 风险提示：
    * - 直接访问IDE内部实现，可能因版本更新而失效
    * - 反射操作可能触发安全异常
    * - 不当使用可能导致IDE不稳定
    * - 违反了插件间的隔离原则
    *
    * 异常处理：
    * - 捕获所有反射相关异常
    * - 记录详细的错误信息
    * - 异常时返回null，不中断程序执行
    *
    * @param pluginId 目标插件的ID（用于日志记录）
    * @param serviceClassName 要查找的服务类的完整类名
    * @return 找到的服务实例，如果未找到或发生异常则返回null
    */
   public static Object b1(String pluginId, String serviceClassName) {
     try {
       // 第一步：获取IntelliJ IDEA的Application实例
       Application app = ApplicationManager.getApplication();

       // 第二步：通过反射获取Application的内部服务注册表字段
       Field servicesField = app.getClass().getDeclaredField("myServices");

       // 第三步：设置字段为可访问状态，绕过private访问限制
       servicesField.setAccessible(true);

       // 第四步：获取服务注册表Map实例
       Map<?, ?> services = (Map<?, ?>)servicesField.get(app);

       // 第五步：遍历所有已注册的服务实例
       for (Map.Entry<?, ?> entry : services.entrySet()) {
         Object service = entry.getValue();

         // 获取服务的类名，用于日志记录和匹配
         String serviceClass = (service != null) ? service.getClass().getName() : "none";
         LOG.info("发现服务: " + serviceClass);

         // 检查当前服务是否匹配目标服务类名
         if (service != null && service.getClass().getName().equals(serviceClassName)) {
           LOG.info("找到匹配的服务: " + serviceClassName);
           return service;
         }
       }

       // 遍历完成但未找到匹配的服务
       LOG.warn("未找到指定的服务: " + serviceClassName);
     } catch (Exception e) {
       // 捕获反射操作过程中的所有异常
       LOG.error("获取服务时发生异常 - 插件ID: " + pluginId + ", 服务类: " + serviceClassName, e);
     }

     // 未找到服务或发生异常时返回null
     return null;
   }
 }