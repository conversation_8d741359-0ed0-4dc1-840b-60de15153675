 package com.baaon;
 import java.security.MessageDigest;

/**
 * 安全SHA1工具类 - 提供SHA1哈希加密功能
 *
 * 主要用途：
 * 1. 为API请求生成SHA1签名
 * 2. 防止API请求被篡改和重放攻击
 * 3. 确保数据传输的完整性
 *
 * 使用场景：
 * - 正式验证API的签名生成
 * - 时间戳 + 密钥的组合签名
 *
 * <AUTHOR>
 * @version 1.0.0
 */
 public class SecuritySHA1Util
 {
    /**
     * 对输入字符串进行SHA1哈希编码
     *
     * 此方法将输入字符串转换为SHA1哈希值的十六进制字符串表示。
     * 主要用于生成API请求的安全签名。
     *
     * @param inStr 要进行SHA1编码的输入字符串
     * @return SHA1哈希值的十六进制字符串，如果出错则返回空字符串
     * @throws Exception 如果编码过程中发生错误
     */
   public static String shaEncode(String inStr) throws Exception {
        // 初始化SHA消息摘要实例
     MessageDigest sha = null;
     try {
       sha = MessageDigest.getInstance("SHA");
     } catch (Exception e) {
       e.printStackTrace();
       return "";
     }
        // 将输入字符串转换为UTF-8字节数组
     byte[] byteArray = inStr.getBytes("UTF-8");
        // 计算SHA1哈希值
     byte[] md5Bytes = sha.digest(byteArray);
        // 将哈希值转换为十六进制字符串
     StringBuffer hexValue = new StringBuffer();
     for (int i = 0; i < md5Bytes.length; i++) {
            // 将字节转换为无符号整数
       int val = md5Bytes[i] & 0xFF;
            // 如果值小于16，需要在前面补0
       if (val < 16) {
         hexValue.append("0");
       }
            // 将整数转换为十六进制字符串并追加
       hexValue.append(Integer.toHexString(val));
     }
        // 返回最终的十六进制字符串
     return hexValue.toString();
   }
 }