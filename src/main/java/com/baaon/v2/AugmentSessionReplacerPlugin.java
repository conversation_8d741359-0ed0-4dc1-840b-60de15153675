 package com.baaon.v2;
 import com.intellij.ide.AppLifecycleListener;
 import com.intellij.openapi.application.ApplicationManager;
 import com.intellij.openapi.diagnostic.Logger;
 import java.util.List;
 import org.jetbrains.annotations.NotNull;

/**
 * Augment会话替换插件 - Augment Assistant的主入口和生命周期管理
 *
 * 核心功能：
 * 1. 作为插件的主入口点，监听IntelliJ IDEA的应用生命周期事件
 * 2. 在适当的时机初始化SessionId替换功能
 * 3. 确保SessionId替换只执行一次，避免重复初始化
 *
 * 生命周期事件：
 * - 构造函数：插件加载时调用
 * - appFrameCreated：应用框架创建时调用
 * - appStarted：应用启动完成时调用
 *
 * 工作原理：
 * 1. 监听多个生命周期事件，确保在合适的时机执行初始化
 * 2. 使用volatile标志位防止重复初始化
 * 3. 在后台线程中执行SessionId替换，避免阻塞UI线程
 *
 * <AUTHOR>
 * @version 1.0.0
 */
 public class AugmentSessionReplacerPlugin
   implements AppLifecycleListener
 {
    /** 日志记录器，用于记录插件初始化过程中的信息和错误 */
   private static final Logger LOG = Logger.getInstance(AugmentSessionReplacerPlugin.class);
    /** 初始化标志位，使用volatile确保线程安全，防止重复初始化 */
   private static volatile boolean isInitialized = false;
    /**
     * 构造函数 - 插件加载时调用
     * 尝试进行初始化，这是第一个可能的初始化时机
     */
   public AugmentSessionReplacerPlugin() {
     initialize();
   }
    /**
     * 应用框架创建事件处理
     * 当IntelliJ IDEA的主框架创建完成时调用
     *
     * @param commandLineArgs 命令行参数列表
     */
   public void appFrameCreated(@NotNull List<String> commandLineArgs) {
     initialize();
   }
    /**
     * 应用启动完成事件处理
     * 当IntelliJ IDEA完全启动后调用，这是最后的初始化时机
     */
   public void appStarted() {
     initialize();
   }
    /**
     * 初始化SessionId替换功能
     *
     * 此方法确保SessionId替换只执行一次，使用双重检查锁定模式
     * 在后台线程中执行实际的替换操作，避免阻塞UI线程
     */
   private void initialize() {
        // 双重检查锁定：只有在未初始化时才进行初始化
     if (!isInitialized) {
       try {
         LOG.info("Starting Augment Session ID Replacer Plugin...");
            // 在后台线程池中执行SessionId替换，避免阻塞UI线程
         ApplicationManager.getApplication().executeOnPooledThread(() -> {
               try {
                    // 创建SessionId替换器并执行替换操作
                 SessionIdReplacer replacer = new SessionIdReplacer();
                 if (replacer.replaceSessionIdClass()) {
                   LOG.info("Successfully replaced SessionId class");
                        // 替换成功，设置初始化标志
                   isInitialized = true;
                 } else {
                   LOG.warn("Failed to replace SessionId class");
                 }
               } catch (Exception e) {
                 LOG.error("Error during SessionId class replacement", e);
               }
             });
       } catch (Exception e) {
         LOG.error("Failed to initialize Augment Session ID Replacer", e);
       }
     }
   }

    /**
     * 获取插件的单例实例
     *
     * 通过IntelliJ IDEA的服务机制获取插件实例，
     * 确保在整个应用生命周期中只有一个插件实例。
     *
     * @return AugmentSessionReplacerPlugin的单例实例
     */
   public static AugmentSessionReplacerPlugin getInstance() {
     return (AugmentSessionReplacerPlugin)ApplicationManager.getApplication().getService(AugmentSessionReplacerPlugin.class);
   }
 }