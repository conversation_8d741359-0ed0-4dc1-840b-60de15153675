 package com.baaon.v2;
 import com.intellij.ide.util.PropertiesComponent;
 import com.intellij.openapi.application.PermanentInstallationID;
 import com.intellij.openapi.diagnostic.Logger;
 import java.util.UUID;
 import org.jetbrains.annotations.NotNull;
/**
 * SessionId管理类 - Augment Assistant插件的核心会话管理组件
 *
 * 功能说明：
 * 1. 管理Augment Code插件的SessionId，支持多种来源的SessionId获取策略
 * 2. 实现试用SessionId和正式SessionId的优先级管理
 * 3. 提供SessionId的重置、存储和状态查询功能
 *
 * SessionId优先级策略（从高到低）：
 * 1. 试用SessionId（如果试用期有效）
 * 2. 用户配置的存储SessionId
 * 3. IntelliJ IDEA的永久安装ID
 * 4. 随机生成的UUID（作为最后备选）
 *
 * <AUTHOR>
 * @version 1.0.0
 */
 public class SessionId
 {
    /** 日志记录器，用于记录SessionId相关的操作日志 */
   private static final Logger LOG = Logger.getInstance(SessionId.class); @NotNull
    /** SessionId类的单例实例，确保全局唯一性 */
   public static final SessionId INSTANCE = new SessionId();
   @NotNull
    /** SessionId在IntelliJ IDEA配置中的存储键名 */
   private static final String SESSION_ID_KEY = "augment.session.id";
    /**
     * 获取当前有效的SessionId
     *
     * 实现策略：
     * 1. 优先使用试用SessionId（如果试用期有效）
     * 2. 其次使用用户存储的SessionId
     * 3. 再次使用IntelliJ IDEA的永久安装ID
     * 4. 最后生成新的随机UUID并存储
     *
     * @return 当前有效的SessionId字符串
     */
   @NotNull
   public String getSessionId() {
     LOG.info("获取SessionId");

        // 第一优先级：检查试用SessionId
     TrialSessionManager trialManager = TrialSessionManager.getInstance();
     if (trialManager.hasValidTrialSession()) {
       String trialSessionId = trialManager.getTrialSessionId();
       if (trialSessionId != null && !isBlank(trialSessionId)) {
         LOG.info("使用试用SessionId: " + trialSessionId);
         return trialSessionId;
       }
     }
        // 第二优先级：检查用户存储的SessionId
     PropertiesComponent properties = PropertiesComponent.getInstance();
     String storedSessionID = properties.getValue("augment.session.id");
     if (storedSessionID != null && !isBlank(storedSessionID)) {
       return storedSessionID;
     }

        // 第三优先级：使用IntelliJ IDEA的永久安装ID
     String installationID = PermanentInstallationID.get();
     if (!isBlank(installationID)) {
       return installationID;
     }

        // 最后备选：生成新的随机UUID并存储
     String newSessionID = UUID.randomUUID().toString();
     properties.setValue("augment.session.id", newSessionID);
     return newSessionID;
   }

    /**
     * 重置SessionId为新的随机UUID
     *
     * 此方法会生成一个新的随机UUID作为SessionId，并将其存储到IntelliJ IDEA的配置中。
     * 通常在用户需要重新生成SessionId时调用。
     *
     * @return 新生成的SessionId字符串
     */
   @NotNull
   public String resetSessionId() {
        // 生成新的随机UUID作为SessionId
     String newSessionId = UUID.randomUUID().toString();
        // 将新的SessionId存储到配置中
     PropertiesComponent.getInstance().setValue("augment.session.id", newSessionId);
     return newSessionId;
   }
    /**
     * 检查字符串是否为空白（null、空字符串或只包含空白字符）
     *
     * @param str 要检查的字符串
     * @return 如果字符串为空白则返回true，否则返回false
     */
   private boolean isBlank(@NotNull String str) {
     return str.trim().isEmpty();
   }

    /**
     * 获取存储的SessionId（不包括试用SessionId）
     *
     * 此方法只返回用户存储的SessionId或IntelliJ IDEA的永久安装ID，
     * 不会返回试用SessionId，主要用于查询非试用状态下的SessionId。
     *
     * @return 存储的SessionId字符串，如果没有则返回空字符串
     */
   @NotNull
   public String getStoredSessionId() {
        // 首先尝试获取用户存储的SessionId
     String storedSessionID = PropertiesComponent.getInstance().getValue("augment.session.id");
     if (storedSessionID != null && !isBlank(storedSessionID)) {
       return storedSessionID;
     }
        // 如果没有存储的SessionId，则使用IntelliJ IDEA的永久安装ID
     String installationID = PermanentInstallationID.get();
     if (installationID != null && !isBlank(installationID)) {
       return installationID;
     }
        // 如果都没有，返回空字符串
     return "";
   }

    /**
     * 检查是否有有效的SessionId
     *
     * 此方法检查是否存在有效的SessionId（用户存储的或永久安装ID），
     * 不包括试用SessionId的检查。
     *
     * @return 如果有有效的SessionId则返回true，否则返回false
     */
   public boolean hasValidSessionId() {
        // 检查用户存储的SessionId
     String storedSessionID = PropertiesComponent.getInstance().getValue("augment.session.id");
     if (storedSessionID != null && !isBlank(storedSessionID)) {
       return true;
     }
        // 检查IntelliJ IDEA的永久安装ID
     String installationID = PermanentInstallationID.get();
     return (installationID != null && !isBlank(installationID));
   }

    /**
     * 清除存储的SessionId
     *
     * 此方法会从IntelliJ IDEA的配置中删除用户存储的SessionId，
     * 但不会影响永久安装ID或试用SessionId。
     */
   public void clearStoredSessionId() {
     PropertiesComponent.getInstance().unsetValue("augment.session.id");
   }

    /**
     * 获取当前SessionId的来源类型
     *
     * 此方法返回当前使用的SessionId的来源，用于调试和日志记录。
     *
     * @return SessionId来源的字符串描述：
     *         - "TrialSession": 试用SessionId
     *         - "PropertiesComponent": 用户存储的SessionId
     *         - "PermanentInstallationID": IntelliJ IDEA永久安装ID
     *         - "Generated": 新生成的随机UUID
     */
   @NotNull
   public String getSessionIdSource() {
        // 检查是否使用试用SessionId
     TrialSessionManager trialManager = TrialSessionManager.getInstance();
     if (trialManager.hasValidTrialSession()) {
       return "TrialSession";
     }
        // 检查是否使用用户存储的SessionId
     String storedSessionID = PropertiesComponent.getInstance().getValue("augment.session.id");
     if (storedSessionID != null && !isBlank(storedSessionID)) {
       return "PropertiesComponent";
     }
        // 检查是否使用永久安装ID，否则为生成的UUID
     String installationID = PermanentInstallationID.get();
     return !isBlank(installationID) ? "PermanentInstallationID" : "Generated";
   }

    /**
     * 获取SessionId的详细信息
     *
     * 此方法返回包含SessionId值和来源的格式化字符串，
     * 主要用于调试和用户界面显示。
     *
     * @return 格式化的SessionId信息字符串
     */
   @NotNull
   public String getSessionIdInfo() {
        // 获取当前SessionId和其来源
     String sessionId = getSessionId();
     String source = getSessionIdSource();
        // 返回格式化的信息字符串
     return String.format("SessionID: %s (Source: %s)", new Object[] { sessionId, source });
   }
 }