 package com.baaon.v2;
 import com.intellij.ide.util.PropertiesComponent;
 import com.intellij.openapi.application.PermanentInstallationID;
 import com.intellij.openapi.diagnostic.Logger;
 import java.time.Instant;
 import java.time.temporal.ChronoUnit;
 import java.util.UUID;
 import org.jetbrains.annotations.NotNull;
 import org.jetbrains.annotations.Nullable;

/**
 * 试用会话管理器 - 管理Augment Assistant的试用认证功能
 *
 * 核心功能：
 * 1. 试用验证码的激活和验证（固定验证码：1024）
 * 2. 试用期限管理（3天试用期）
 * 3. 试用SessionId的生成和管理
 * 4. 试用数据的存储和清理
 *
 * 试用机制说明：
 * - 试用验证码：固定为"1024"
 * - 试用期限：3天（从激活时开始计算）
 * - 试用SessionId：激活时生成的随机UUID
 * - 自动过期：超过3天后自动清理试用数据
 *
 * 存储键名：
 * - augment.trial.code: 试用验证码
 * - augment.trial.start.time: 试用开始时间戳
 * - augment.trial.session.id: 试用SessionId
 * - augment.original.session.id: 原始SessionId（备份）
 *
 * <AUTHOR>
 * @version 1.0.0
 */
 public class TrialSessionManager
 {
   private static final Logger LOG = Logger.getInstance(TrialSessionManager.class);
   private static final String TRIAL_CODE_KEY = "augment.trial.code";
   private static final String TRIAL_START_TIME_KEY = "augment.trial.start.time";
   private static final String TRIAL_SESSION_ID_KEY = "augment.trial.session.id";
   private static final String ORIGINAL_SESSION_ID_KEY = "augment.original.session.id";
   private static final int TRIAL_DAYS = 3;
   private static final TrialSessionManager INSTANCE = new TrialSessionManager();
 
 
   
   /**
    * 获取TrialSessionManager的单例实例
    *
    * 此方法提供对TrialSessionManager单例实例的访问。
    * 使用单例模式确保整个应用中只有一个试用会话管理器实例，
    * 保证试用状态的一致性和数据的完整性。
    *
    * @return TrialSessionManager的单例实例，保证非空
    */
   @NotNull
   public static TrialSessionManager getInstance() {
     return INSTANCE;
   }
   
     /**
     * 激活试用验证码
     *
     * 此方法用于激活用户输入的试用验证码，设置试用期开始时间，
     * 生成试用专用的SessionId，并保存相关配置信息。
     *
     * @param trialCode 试用验证码
     * @return 如果激活成功返回true，否则返回false
     */
  public boolean activateTrialCode(String trialCode) {
        // 第一步：验证输入参数
        // 检查验证码是否为null
     if (trialCode == null) return false;
        // 检查验证码是否为空字符串（去除空白字符后）
     if (trialCode.trim().isEmpty()) {
       return false;
     }

        // 第二步：检查是否已经激活过试用验证码
     PropertiesComponent properties = PropertiesComponent.getInstance();
        // 获取已存储的试用验证码
     String existingCode = properties.getValue("augment.trial.code");
        // 如果已经存在有效的试用验证码，无需重复激活
     if (existingCode != null && !existingCode.trim().isEmpty()) {
       LOG.info("试用验证码已存在，无需重复激活");
       return true;
     }

        // 第三步：保存当前SessionId并生成试用SessionId
        // 获取当前的SessionId作为原始SessionId备份（避免循环依赖，直接获取存储的值）
     String currentSessionId = getCurrentSessionIdDirect();
        // 保存原始SessionId，以便试用期结束后恢复
     properties.setValue("augment.original.session.id", currentSessionId);
        // 生成新的随机UUID作为试用期专用SessionId
     String trialSessionId = UUID.randomUUID().toString();
        // 保存试用SessionId
     properties.setValue("augment.trial.session.id", trialSessionId);

        // 第四步：保存试用验证码和开始时间
        // 保存试用验证码
     properties.setValue("augment.trial.code", trialCode);
        // 保存试用开始时间戳（毫秒）
     properties.setValue("augment.trial.start.time", String.valueOf(Instant.now().toEpochMilli()));
        // 记录激活成功信息
     LOG.info("试用验证码激活成功: " + trialCode + ", 试用SessionId: " + trialSessionId);
     return true;
   }
 
 
   
   /**
    * 检查是否存在有效的试用会话
    *
    * 此方法检查当前是否存在有效的试用会话，包括以下验证：
    * 1. 检查试用验证码是否存在且有效
    * 2. 检查试用开始时间是否存在且可解析
    * 3. 计算试用期是否在3天有效期内
    * 4. 如果试用期已过期，自动清理试用数据
    *
    * 验证逻辑：
    * - 试用期限：3天（从激活时开始计算）
    * - 时间计算：使用ChronoUnit.DAYS进行精确的天数计算
    * - 自动清理：过期时自动调用clearTrialData()清理数据
    *
    * @return 如果存在有效的试用会话返回true，否则返回false
    */
   public boolean hasValidTrialSession() {
     // 获取配置组件实例
     PropertiesComponent properties = PropertiesComponent.getInstance();

     // 获取试用验证码和开始时间
     String trialCode = properties.getValue("augment.trial.code");
     String startTimeStr = properties.getValue("augment.trial.start.time");

     // 检查基本数据是否存在
     if (trialCode != null && !trialCode.trim().isEmpty() && startTimeStr != null) {
       try {
         // 解析试用开始时间戳
         long startTime = Long.parseLong(startTimeStr);
         Instant startInstant = Instant.ofEpochMilli(startTime);
         Instant now = Instant.now();

         // 计算已过去的天数
         long daysPassed = ChronoUnit.DAYS.between(startInstant, now);

         // 检查是否在3天试用期内
         boolean isValid = (daysPassed < 3L);

         // 如果试用期已过期，清理试用数据
         if (!isValid) {
           LOG.info("试用期已过期，已过去 " + daysPassed + " 天");
           clearTrialData();
         }

         return isValid;
       } catch (NumberFormatException e) {
         // 时间戳解析失败，记录错误并返回false
         LOG.error("解析试用开始时间失败", e);
         return false;
       }
     }
     return false;
   }
   
   /**
    * 获取试用SessionId
    *
    * 此方法返回当前有效试用会话的SessionId。
    * 只有在试用会话有效的情况下才会返回SessionId，
    * 否则返回null。
    *
    * 使用场景：
    * - SessionId管理类需要获取试用SessionId时
    * - 验证当前是否使用试用SessionId时
    * - 显示当前SessionId信息时
    *
    * @return 如果试用会话有效则返回试用SessionId，否则返回null
    */
   @Nullable
   public String getTrialSessionId() {
     // 首先检查试用会话是否有效
     if (!hasValidTrialSession()) {
       return null;
     }

     // 获取并返回试用SessionId
     PropertiesComponent properties = PropertiesComponent.getInstance();
     return properties.getValue("augment.trial.session.id");
   }
   
   /**
    * 获取原始SessionId
    *
    * 此方法返回激活试用会话前保存的原始SessionId。
    * 原始SessionId在试用期结束后可用于恢复到试用前的状态。
    *
    * 使用场景：
    * - 试用期结束后恢复原始SessionId
    * - 显示试用前的SessionId信息
    * - 调试和问题排查时查看SessionId变化
    *
    * @return 原始SessionId，如果没有保存则返回null
    */
   @Nullable
   public String getOriginalSessionId() {
     PropertiesComponent properties = PropertiesComponent.getInstance();
     return properties.getValue("augment.original.session.id");
   }
   /**
    * 获取试用验证码
    *
    * 此方法返回当前保存的试用验证码。
    * 注意：此方法不检查试用期是否有效，只是简单返回存储的验证码。
    *
    * 使用场景：
    * - 检查是否已激活过试用验证码
    * - 显示当前的试用验证码信息
    * - 验证和调试试用状态
    *
    * @return 试用验证码，如果没有激活过则返回null
    */
   @Nullable
   public String getTrialCode() {
     PropertiesComponent properties = PropertiesComponent.getInstance();
     return properties.getValue("augment.trial.code");
   }
   
   /**
    * 获取试用期剩余天数
    *
    * 此方法计算并返回试用期的剩余天数。
    * 只有在试用会话有效的情况下才会进行计算，
    * 否则返回0。
    *
    * 计算逻辑：
    * - 总试用期：3天
    * - 计算方式：3 - 已过去的天数
    * - 最小值：0（不会返回负数）
    *
    * 使用场景：
    * - 在UI中显示剩余试用天数
    * - 判断试用期即将到期的提醒
    * - 试用状态的详细信息展示
    *
    * @return 试用期剩余天数，如果试用无效或已过期则返回0
    */
   public int getRemainingDays() {
     // 检查试用会话是否有效
     if (!hasValidTrialSession()) {
       return 0;
     }

     // 获取试用开始时间
     PropertiesComponent properties = PropertiesComponent.getInstance();
     String startTimeStr = properties.getValue("augment.trial.start.time");
     if (startTimeStr == null) {
       return 0;
     }

     try {
       // 解析开始时间并计算已过去的天数
       long startTime = Long.parseLong(startTimeStr);
       Instant startInstant = Instant.ofEpochMilli(startTime);
       Instant now = Instant.now();
       long daysPassed = ChronoUnit.DAYS.between(startInstant, now);

       // 计算剩余天数，确保不返回负数
       return Math.max(0, 3 - (int)daysPassed);
     } catch (NumberFormatException e) {
       LOG.error("解析试用开始时间失败", e);
       return 0;
     }
   }
 
 
   
   /**
    * 清理试用数据
    *
    * 此方法清除所有与试用相关的存储数据，包括：
    * - 试用验证码
    * - 试用开始时间
    * - 试用SessionId
    * - 原始SessionId备份
    *
    * 使用场景：
    * - 试用期过期时自动清理
    * - 用户手动重置试用状态
    * - 正式验证成功后清理试用数据
    * - 插件卸载或重新初始化时
    *
    * 注意：此操作不可逆，清理后需要重新激活试用验证码
    */
   public void clearTrialData() {
     PropertiesComponent properties = PropertiesComponent.getInstance();

     // 清除试用验证码
     properties.unsetValue("augment.trial.code");

     // 清除试用开始时间
     properties.unsetValue("augment.trial.start.time");

     // 清除试用SessionId
     properties.unsetValue("augment.trial.session.id");

     // 清除原始SessionId备份
     properties.unsetValue("augment.original.session.id");

     // 记录清理操作
     LOG.info("试用数据已清理");
   }
   
   /**
    * 检查是否存在试用验证码
    *
    * 此方法检查是否已经激活过试用验证码，
    * 不检查试用期是否有效，只检查验证码是否存在。
    *
    * 与hasValidTrialSession()的区别：
    * - hasTrialCode()：只检查验证码是否存在
    * - hasValidTrialSession()：检查验证码存在且试用期有效
    *
    * 使用场景：
    * - 判断用户是否曾经激活过试用
    * - 区分"从未试用"和"试用已过期"的状态
    * - UI状态显示和逻辑判断
    *
    * @return 如果存在试用验证码返回true，否则返回false
    */
   public boolean hasTrialCode() {
     PropertiesComponent properties = PropertiesComponent.getInstance();
     String trialCode = properties.getValue("augment.trial.code");
     return (trialCode != null && !trialCode.trim().isEmpty());
   }

   /**
    * 直接获取当前SessionId（避免循环依赖的内部方法）
    *
    * 此方法复制了SessionId类的获取逻辑，但不依赖SessionId.INSTANCE，
    * 避免在试用会话激活过程中产生循环依赖问题。
    *
    * 获取策略（与SessionId类保持一致）：
    * 1. 优先使用用户存储的SessionId
    * 2. 其次使用IntelliJ IDEA的永久安装ID
    * 3. 最后生成新的随机UUID
    *
    * 使用场景：
    * - 试用验证码激活时保存原始SessionId
    * - 避免与SessionId类的循环依赖
    * - 确保获取逻辑的一致性
    *
    * 注意：此方法为私有方法，仅供内部使用
    *
    * @return 当前的SessionId，保证非空
    */
   @NotNull
   private String getCurrentSessionIdDirect() {
     PropertiesComponent properties = PropertiesComponent.getInstance();

     // 第一优先级：检查用户存储的SessionId
     String storedSessionID = properties.getValue("augment.session.id");
     if (storedSessionID != null && !storedSessionID.trim().isEmpty()) {
       return storedSessionID;
     }

     // 第二优先级：使用IntelliJ IDEA的永久安装ID
     try {
       String installationId = PermanentInstallationID.get();
       if (installationId != null && !installationId.trim().isEmpty()) {
         return installationId;
       }
     } catch (Exception e) {
       // 获取永久安装ID失败时记录警告，但不中断流程
       LOG.warn("获取永久安装ID失败", e);
     }

     // 最后备选：生成随机UUID确保始终返回有效值
     return UUID.randomUUID().toString();
   }
 }