 package com.baaon.v2;
 import com.intellij.ide.plugins.IdeaPluginDescriptor;
 import com.intellij.ide.plugins.PluginManager;
 import com.intellij.openapi.application.Application;
 import com.intellij.openapi.application.ApplicationManager;
 import com.intellij.openapi.diagnostic.Logger;
 import java.lang.reflect.Constructor;
 import java.lang.reflect.Field;
 import java.lang.reflect.Method;
/**
 * SessionId替换器 - 负责动态替换Augment Code插件中的SessionId
 *
 * 核心功能：
 * 1. 通过反射机制访问com.augmentcode插件的内部组件
 * 2. 动态替换AugmentHttpClient实例中的SessionId
 * 3. 实现跨插件的类加载器操作和对象注入
 *
 * 工作原理：
 * 1. 获取目标插件(com.augmentcode)的类加载器
 * 2. 通过反射获取AugmentAPI服务实例
 * 3. 访问其httpClient字段并替换为新的实例
 * 4. 新实例使用当前配置的SessionId进行初始化
 *
 * 注意事项：
 * - 此类大量使用反射操作，可能存在兼容性风险
 * - 依赖于目标插件的内部实现细节
 * - 需要在目标插件加载完成后执行
 *
 * <AUTHOR>
 * @version 1.0.0
 */
 public class SessionIdReplacer
 {
    /** 日志记录器，用于记录SessionId替换过程中的操作和错误 */
   private static final Logger LOG = Logger.getInstance(SessionIdReplacer.class);

    /**
     * 重新初始化HttpClient实例
     *
     * 此方法是SessionId替换的核心实现，通过以下步骤完成替换：
     * 1. 获取目标插件的类加载器
     * 2. 通过反射获取AugmentAPI服务实例
     * 3. 访问httpClient字段并创建新的实例
     * 4. 使用当前配置的SessionId初始化新实例
     *
     * @return 如果替换成功返回true，否则返回false
     */
   private boolean reinitializeHttpClient() {
     try {
        // 保存当前线程的类加载器，以便后续恢复
       ClassLoader originalClassLoader = Thread.currentThread().getContextClassLoader();
       ClassLoader targetClassLoader = null;

        // 第一步：获取目标插件的类加载器
       try {
         PluginManager pluginManager = PluginManager.getInstance();

        // 查找com.augmentcode插件
         IdeaPluginDescriptor targetPlugin = null;
         IdeaPluginDescriptor[] allPlugins = PluginManager.getPlugins();

        // 遍历所有已安装的插件，寻找目标插件
         for (IdeaPluginDescriptor plugin : allPlugins) {
           if ("com.augmentcode".equals(plugin.getPluginId().getIdString())) {
             targetPlugin = plugin;
             break;
           }
         }
        // 如果找到目标插件，获取其类加载器
         if (targetPlugin != null) {
           targetClassLoader = targetPlugin.getPluginClassLoader();
           LOG.info("成功获取目标插件的类加载器");
         }
        // 如果无法获取目标插件的类加载器，记录警告并继续
       } catch (Exception e) {
         LOG.warn("无法获取目标插件类加载器，将使用当前类加载器: " + e.getMessage());
       }
        // 如果无法获取目标插件的类加载器，使用当前类的类加载器作为备选
       if (targetClassLoader == null) {
         targetClassLoader = getClass().getClassLoader();
       }
        // 第二步：设置线程上下文类加载器并通过反射访问目标插件
        // 设置当前线程的上下文类加载器为目标插件的类加载器
       Thread.currentThread().setContextClassLoader(targetClassLoader);
        // 通过反射加载AugmentAPI类，第三个参数true表示需要初始化类
       Class<?> apiImplClass = Class.forName("com.augmentcode.intellij.api.AugmentAPI", true, targetClassLoader);
        // 获取IntelliJ IDEA的应用程序实例
       Application app = ApplicationManager.getApplication();
        // 获取应用程序的getService方法，用于获取服务实例
       Method method = app.getClass().getMethod("getService", new Class[] { Class.class });
        // 调用getService方法获取AugmentAPI服务实例
       Object invoke = method.invoke(app, new Object[] { apiImplClass });

        // 第三步：访问和替换httpClient字段
        // 通过反射获取AugmentAPI实例中的httpClient字段
       Field httpClientField = invoke.getClass().getDeclaredField("httpClient");
        // 设置字段可访问（绕过private访问限制）
       httpClientField.setAccessible(true);
        // 获取当前配置的SessionId
       String sessionId = SessionId.INSTANCE.getSessionId();
        // 记录使用的SessionId和其来源信息
       LOG.info("使用配置的SessionId: " + sessionId + " (来源: " + SessionId.INSTANCE.getSessionIdSource() + ")");

        // 第四步：创建新的HttpClient实例并替换
        // 加载AugmentHttpClient类
       Class<?> httpClientClass = Class.forName("com.augmentcode.intellij.api.AugmentHttpClient");
        // 获取接受String参数的构造函数
       Constructor<?> constructor = httpClientClass.getConstructor(new Class[] { String.class });
        // 使用新的SessionId创建新的HttpClient实例
       Object newHttpClient = constructor.newInstance(new Object[] { sessionId });
        // 将新的HttpClient实例设置到AugmentAPI的httpClient字段中
       httpClientField.set(invoke, newHttpClient);
        // 记录成功信息
       LOG.info("成功重新初始化httpClient实例");

        // 第五步：恢复原始类加载器并返回成功
        // 恢复线程的原始上下文类加载器
       Thread.currentThread().setContextClassLoader(originalClassLoader);
       return true;
     } catch (Exception e) {
       LOG.error("重新初始化httpClient实例失败", e);
       return false;
     } 
   }
    /**
     * 执行SessionId类替换操作
     *
     * 此方法是SessionId替换的主入口，负责协调整个替换过程。
     * 目前主要通过重新初始化HttpClient的方式来实现SessionId的替换。
     *
     * @return 如果替换成功返回true，否则返回false
     */
   public boolean replaceSessionIdClass() {
     try {
            // 尝试通过重新初始化HttpClient来替换SessionId
       if (reinitializeHttpClient()) {
                // 如果重新初始化成功，返回true
         return true;
       }
            // 如果所有替换方法都失败，记录警告
       LOG.warn("所有替换方法都失败");
       return false;
     }
        // 捕获替换过程中的任何异常
     catch (Exception e) {
            // 记录错误信息并返回失败
       LOG.error("替换SessionId类时出错", e);
       return false;
     }
   }
 }