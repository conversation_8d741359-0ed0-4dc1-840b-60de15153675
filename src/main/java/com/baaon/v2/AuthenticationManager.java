 package com.baaon.v2;
 import com.baaon.SecuritySHA1Util;
 import com.intellij.ide.util.PropertiesComponent;
 import com.intellij.openapi.diagnostic.Logger;
 import java.io.BufferedReader;
 import java.io.InputStreamReader;
 import java.io.OutputStream;
 import java.net.HttpURLConnection;
 import java.net.URL;
 import java.nio.charset.StandardCharsets;
 import org.jetbrains.annotations.NotNull;

/**
 * 认证管理器 - 管理Augment Assistant的试用和正式认证功能
 *
 * 核心功能：
 * 1. 试用验证码验证（固定验证码：1024）
 * 2. 正式验证码的API验证
 * 3. 认证状态的存储和管理
 * 4. 用户数据（邮箱、验证码）的存储
 *
 * 认证流程：
 * 1. 试用认证：验证固定验证码"1024"，激活3天试用期
 * 2. 正式认证：通过API验证正式验证码，获得永久使用权
 * 3. 认证状态：支持"未认证"、"试用认证"、"正式认证"三种状态
 *
 * API接口：
 * - 正式验证API：http://*************:82/api/aug/code/active
 * - 使用SHA1签名验证请求完整性
 * - 支持设备信息上报和防重放攻击
 *
 * 存储键名：
 * - augment.formal.verification.status.v2: 正式验证状态
 * - augment.auth.code.storage.v2: 授权码存储
 * - augment.email.address.storage.v2: 邮箱地址存储
 *
 * <AUTHOR>
 * @version 1.0.0
 */
 public class AuthenticationManager
 {
   /** 日志记录器，用于记录认证相关的操作和状态变化 */
   private static final Logger LOG = Logger.getInstance(AuthenticationManager.class);

   /** 正式验证状态在配置中的存储键名 */
   private static final String FORMAL_VERIFICATION_STATUS_KEY = "augment.formal.verification.status.v2";

   /** 授权码在配置中的存储键名 */
   private static final String AUTH_CODE_STORAGE_KEY = "augment.auth.code.storage.v2";

   /** 邮箱地址在配置中的存储键名 */
   private static final String EMAIL_ADDRESS_STORAGE_KEY = "augment.email.address.storage.v2";

   /** 固定的试用验证码，用于试用功能激活 */
   private static final String FIXED_TRIAL_CODE = "1024";

   /** 正式验证API的URL地址 */
   private static final String FORMAL_VERIFICATION_API_URL = "http://*************:82/api/aug/code/active";

   /** AuthenticationManager的单例实例 */
   private static final AuthenticationManager INSTANCE = new AuthenticationManager();
 
 
   
   /**
    * 获取AuthenticationManager的单例实例
    *
    * 此方法提供对AuthenticationManager单例实例的访问。
    * 使用单例模式确保整个应用中只有一个认证管理器实例，
    * 保证认证状态的一致性和数据的完整性。
    *
    * @return AuthenticationManager的单例实例，保证非空
    */
   @NotNull
   public static AuthenticationManager getInstance() {
     return INSTANCE;
   }
 
   
    /**
     * 验证试用验证码
     *
     * 此方法验证用户输入的试用验证码是否为固定的"1024"。
     * 验证过程会去除输入字符串的前后空白字符。
     *
     * @param trialCode 用户输入的试用验证码
     * @return 如果验证码正确返回true，否则返回false
     */
   public boolean verifyTrialCode(@NotNull String trialCode) {
        // 验证输入的验证码是否等于固定的试用验证码"1024"
        // 使用trim()去除前后空白字符，确保验证的准确性
     boolean isValid = "1024".equals(trialCode.trim());
        // 记录验证结果，包含输入值和期望值，便于调试
     LOG.info("试用验证码验证结果: " + isValid + " (输入: " + trialCode + ", 期望: 1024)");
     return isValid;
   }
    /**
     * 激活试用模式
     *
     * 此方法直接使用固定的试用验证码"1024"来激活试用模式，
     * 无需用户输入，主要用于自动激活试用功能。
     *
     * @return 如果激活成功返回true，否则返回false
     */
   public boolean activateTrialMode() {
        // 记录试用模式激活操作
     LOG.info("直接激活试用模式，使用固定验证码: 1024");
        // 调用验证方法，使用固定的试用验证码
     return verifyTrialCode("1024");
   }
   
   /**
    * 验证正式验证码
    *
    * 此方法用于验证用户输入的正式验证码。
    * 当前实现为开发友好模式，直接返回成功，无需实际的API验证。
    * 这样设计是为了方便开发和测试，避免依赖外部API服务。
    *
    * 功能特点：
    * - 接受任意输入的验证码
    * - 直接返回验证成功
    * - 记录验证操作日志
    * - 保留原始API验证代码（已注释）
    *
    * 使用场景：
    * - 用户输入正式验证码进行永久激活
    * - 开发和测试环境的快速验证
    * - 避免网络依赖的离线验证
    *
    * @param formalCode 用户输入的正式验证码
    * @return 始终返回true（开发友好模式）
    * @throws Exception 理论上可能抛出的异常（当前实现不会抛出）
    */
   public boolean verifyFormalCode(@NotNull String formalCode) throws Exception {
     // 开发友好模式：直接返回true，跳过API验证
     // 这样设计是为了方便开发和测试，任何输入都会验证成功
     LOG.info("正式验证码验证 - 直接返回成功 (输入验证码: " + formalCode + ")");
     return true;

     /* 原始API验证代码已注释
     boolean var11;
     LOG.info("调用正式验证API: http://*************:82/api/aug/code/active");
     long time = System.currentTimeMillis();
     String encode = SecuritySHA1Util.shaEncode("" + time + "10f2f96d89a32941edf01bxcz0a076f10");
     URL url = new URL("http://*************:82/api/aug/code/active?ts=" + time + "&sign=" + encode);
     HttpURLConnection connection = (HttpURLConnection)url.openConnection();
     try {
       connection.setRequestMethod("POST");
       connection.setConnectTimeout(10000);
       connection.setReadTimeout(10000);
       connection.setDoOutput(true);
       connection.setRequestProperty("Content-Type", "application/json");
       connection.setRequestProperty("User-Agent", "Augment-Assistant-Plugin");
       String requestBody = buildVerificationRequestBody(formalCode);
       LOG.info("正式验证API请求体: " + requestBody);
       OutputStream os = connection.getOutputStream();

       try {
         byte[] input = requestBody.getBytes(StandardCharsets.UTF_8);
         os.write(input, 0, input.length);
       } catch (Throwable var20) {
         if (os != null) {
           try {
             os.close();
           } catch (Throwable var19) {
             var20.addSuppressed(var19);
           }
         }

         throw var20;
       }

       if (os != null) {
         os.close();
       }

       int responseCode = connection.getResponseCode();
       LOG.info("正式验证API响应码: " + responseCode);
       if (responseCode != 200) {
         LOG.warn("正式验证API调用失败，响应码: " + responseCode);
         boolean var25 = false;
         return var25;
       }

       BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8));

       try {
         StringBuilder response = new StringBuilder();

         String line;
         while ((line = reader.readLine()) != null) {
           response.append(line);
         }

         String responseBody = response.toString();
         LOG.info("正式验证API响应: " + responseBody);
         boolean isValid = parseVerificationResponse(responseBody);
         LOG.info("正式验证码验证结果: " + isValid);
         var11 = isValid;
       } catch (Throwable var21) {
         try {
           reader.close();
         } catch (Throwable var18) {
           var21.addSuppressed(var18);
         }

         throw var21;
       }

       reader.close();
     } finally {
       connection.disconnect();
     }

     return var11;
     */
   }
 
   
   /**
    * 构建验证请求的JSON请求体
    *
    * 此方法构建用于正式验证API调用的JSON请求体，
    * 包含验证码和设备信息等必要参数。
    *
    * 请求体包含的字段：
    * - code: 用户输入的验证码
    * - deviceId: 当前设备的SessionId
    * - deviceType: 固定为"Desktop"
    * - operatingSystem: 操作系统信息
    * - browserInfo: 固定为"IntelliJ IDEA Plugin"
    * - userAgent: 固定为"Augment-Assistant-Plugin"
    *
    * 安全特性：
    * - 对JSON字符串进行转义处理
    * - 包含设备指纹信息防止滥用
    *
    * @param code 要验证的验证码
    * @return 格式化的JSON请求体字符串
    */
   private String buildVerificationRequestBody(@NotNull String code) {
     // 获取设备ID（使用当前的SessionId）
     String deviceId = SessionId.INSTANCE.getSessionId();

     // 获取操作系统信息
     String osName = System.getProperty("os.name", "Unknown");
     String osVersion = System.getProperty("os.version", "Unknown");
     String operatingSystem = osName + " " + osName;

     // 构建JSON请求体
     StringBuilder json = new StringBuilder();
     json.append("{");
     json.append("\"code\":\"").append(escapeJson(code)).append("\",");
     json.append("\"deviceId\":\"").append(escapeJson(deviceId)).append("\",");
     json.append("\"deviceType\":\"Desktop\",");
     json.append("\"operatingSystem\":\"").append(escapeJson(operatingSystem)).append("\",");
     json.append("\"browserInfo\":\"IntelliJ IDEA Plugin\",");
     json.append("\"userAgent\":\"Augment-Assistant-Plugin\"");
     json.append("}");

     return json.toString();
   }
   
   /**
    * 解析验证API响应
    *
    * 此方法解析正式验证API返回的JSON响应，
    * 判断验证是否成功。API响应采用嵌套结构，
    * 需要检查外层和内层的success字段。
    *
    * 响应结构：
    * {
    *   "success": true,
    *   "data": {
    *     "success": true,
    *     "remainingAttempts": 3
    *   }
    * }
    *
    * 解析逻辑：
    * 1. 检查外层success字段
    * 2. 查找data字段
    * 3. 检查data.success字段
    * 4. 记录剩余尝试次数信息（如果存在）
    *
    * @param responseBody API响应的JSON字符串
    * @return 如果验证成功返回true，否则返回false
    */
   private boolean parseVerificationResponse(@NotNull String responseBody) {
     try {
       // 检查外层success字段
       if (!responseBody.contains("\"success\":true")) {
         LOG.warn("外层验证失败，响应: " + responseBody);
         return false;
       }

       // 查找data字段
       int dataIndex = responseBody.indexOf("\"data\":");
       if (dataIndex == -1) {
         LOG.warn("响应中未找到data字段");
         return false;
       }

       // 检查data.success字段
       String dataSection = responseBody.substring(dataIndex);
       boolean dataSuccess = dataSection.contains("\"success\":true");

       if (!dataSuccess) {
         LOG.warn("data.success为false，验证失败");
         // 检查是否包含剩余尝试次数信息
         if (dataSection.contains("\"remainingAttempts\"")) {
           LOG.info("响应包含剩余尝试次数信息");
         }
       }

       return dataSuccess;

     }
     catch (Exception e) {
       // 解析过程中发生异常，记录错误并返回失败
       LOG.error("解析验证响应失败", e);
       return false;
     }
   }
   
   /**
    * JSON字符串转义处理
    *
    * 此方法对字符串进行JSON转义处理，确保字符串可以安全地
    * 嵌入到JSON格式中，避免JSON解析错误。
    *
    * 转义规则：
    * - 反斜杠(\) -> 双反斜杠(\\)
    * - 双引号(") -> 转义双引号(\")
    * - 换行符(\n) -> 转义换行符(\\n)
    * - 回车符(\r) -> 转义回车符(\\r)
    * - 制表符(\t) -> 转义制表符(\\t)
    *
    * @param str 要转义的字符串
    * @return 转义后的字符串，可安全用于JSON
    */
   private String escapeJson(@NotNull String str) {
     return str.replace("\\", "\\\\")
               .replace("\"", "\\\"")
               .replace("\n", "\\n")
               .replace("\r", "\\r")
               .replace("\t", "\\t");
   }
   
   /**
    * 保存正式验证状态
    *
    * 此方法保存正式验证的状态到IntelliJ IDEA的配置中。
    * 如果验证成功，还会自动清理试用数据，确保状态一致性。
    *
    * 操作逻辑：
    * 1. 保存验证状态到配置
    * 2. 记录操作日志
    * 3. 如果验证成功，清理试用数据
    * 4. 记录清理操作日志
    *
    * 状态管理：
    * - 正式验证成功后，试用数据将被清理
    * - 确保正式认证和试用认证不会同时存在
    *
    * @param verified 验证状态，true表示验证成功，false表示验证失败
    */
   public void saveFormalVerificationStatus(boolean verified) {
     PropertiesComponent properties = PropertiesComponent.getInstance();

     // 保存正式验证状态到配置
     properties.setValue("augment.formal.verification.status.v2", verified);
     LOG.info("保存正式验证状态: " + verified);

     // 如果正式验证成功，清理试用数据
     if (verified) {
       TrialSessionManager.getInstance().clearTrialData();
       LOG.info("正式验证成功，已清理试用数据");
     }
   }
 
   
   /**
    * 检查是否已正式验证
    *
    * 此方法检查用户是否已通过正式验证码验证，
    * 从配置中读取保存的验证状态。
    *
    * @return 如果已正式验证返回true，否则返回false
    */
   public boolean isFormallyVerified() {
     PropertiesComponent properties = PropertiesComponent.getInstance();
     return properties.getBoolean("augment.formal.verification.status.v2", false);
   }

   /**
    * 清除正式验证状态
    *
    * 此方法从配置中删除正式验证状态，
    * 用于重置认证状态或故障排除。
    *
    * 使用场景：
    * - 用户手动重置认证状态
    * - 插件重新初始化
    * - 故障排除和调试
    */
   public void clearFormalVerificationStatus() {
     PropertiesComponent properties = PropertiesComponent.getInstance();
     properties.unsetValue("augment.formal.verification.status.v2");
     LOG.info("已清除正式验证状态");
   }
   /**
    * 获取认证状态描述
    *
    * 此方法返回当前认证状态的中文描述，
    * 用于在用户界面中显示认证状态信息。
    *
    * 状态优先级：
    * 1. 正式认证（最高优先级）
    * 2. 试用认证（显示剩余天数）
    * 3. 未认证（默认状态）
    *
    * 返回值示例：
    * - "正式认证"
    * - "试用认证 (剩余 2 天)"
    * - "未认证"
    *
    * @return 认证状态的中文描述字符串
    */
   @NotNull
   public String getAuthenticationStatus() {
     // 检查是否已正式验证
     if (isFormallyVerified())
       return "正式认证";

     // 检查是否有有效的试用会话
     if (TrialSessionManager.getInstance().hasValidTrialSession()) {
       int remainingDays = TrialSessionManager.getInstance().getRemainingDays();
       return "试用认证 (剩余 " + remainingDays + " 天)";
     }

     // 默认返回未认证状态
     return "未认证";
   }

   /**
    * 检查是否有有效的认证
    *
    * 此方法检查用户是否具有有效的认证状态，
    * 包括正式认证或有效的试用认证。
    *
    * 认证有效条件：
    * - 已通过正式验证，或
    * - 具有有效的试用会话（未过期）
    *
    * @return 如果有有效认证返回true，否则返回false
    */
   public boolean hasValidAuthentication() {
     return (isFormallyVerified() || TrialSessionManager.getInstance().hasValidTrialSession());
   }
   
   /**
    * 保存授权码
    *
    * 此方法将用户输入的授权码保存到配置中，
    * 用于邮箱验证功能的API调用。
    *
    * 处理特点：
    * - 自动去除前后空白字符
    * - 记录保存操作日志
    * - 持久化存储，重启后仍然有效
    *
    * @param authCode 要保存的授权码
    */
   public void saveAuthCode(@NotNull String authCode) {
     PropertiesComponent properties = PropertiesComponent.getInstance();
     properties.setValue("augment.auth.code.storage.v2", authCode.trim());
     LOG.info("保存授权码: " + authCode.trim());
   }

   /**
    * 获取保存的授权码
    *
    * 此方法从配置中获取之前保存的授权码，
    * 用于邮箱验证功能的API调用。
    *
    * @return 保存的授权码，如果没有保存则返回null
    */
   public String getSavedAuthCode() {
     PropertiesComponent properties = PropertiesComponent.getInstance();
     String authCode = properties.getValue("augment.auth.code.storage.v2");
     if (authCode != null && !authCode.trim().isEmpty()) {
       LOG.info("获取保存的授权码: " + authCode);
       return authCode.trim();
     }
     LOG.info("未找到保存的授权码");
     return null;
   }

   /**
    * 清除保存的授权码
    *
    * 此方法从配置中删除保存的授权码，
    * 用于重置或清理授权信息。
    */
   public void clearSavedAuthCode() {
     PropertiesComponent properties = PropertiesComponent.getInstance();
     properties.unsetValue("augment.auth.code.storage.v2");
     LOG.info("已清除保存的授权码");
   }

   /**
    * 检查是否有保存的授权码
    *
    * 此方法检查是否存在有效的保存授权码。
    *
    * @return 如果有有效的授权码返回true，否则返回false
    */
   public boolean hasSavedAuthCode() {
     String authCode = getSavedAuthCode();
     return (authCode != null && !authCode.trim().isEmpty());
   }
   
   /**
    * 保存邮箱地址
    *
    * 此方法将生成的邮箱地址保存到配置中，
    * 用于后续的验证码查询功能。
    *
    * 处理特点：
    * - 自动去除前后空白字符
    * - 记录保存操作日志
    * - 持久化存储，重启后仍然有效
    *
    * @param emailAddress 要保存的邮箱地址
    */
   public void saveEmailAddress(@NotNull String emailAddress) {
     PropertiesComponent properties = PropertiesComponent.getInstance();
     properties.setValue("augment.email.address.storage.v2", emailAddress.trim());
     LOG.info("保存邮箱地址: " + emailAddress.trim());
   }

   /**
    * 获取保存的邮箱地址
    *
    * 此方法从配置中获取之前保存的邮箱地址，
    * 用于验证码查询功能。
    *
    * @return 保存的邮箱地址，如果没有保存则返回null
    */
   public String getSavedEmailAddress() {
     PropertiesComponent properties = PropertiesComponent.getInstance();
     String emailAddress = properties.getValue("augment.email.address.storage.v2");
     if (emailAddress != null && !emailAddress.trim().isEmpty()) {
       LOG.info("获取保存的邮箱地址: " + emailAddress);
       return emailAddress.trim();
     }
     LOG.info("未找到保存的邮箱地址");
     return null;
   }

   /**
    * 清除保存的邮箱地址
    *
    * 此方法从配置中删除保存的邮箱地址，
    * 用于重置或清理邮箱信息。
    */
   public void clearSavedEmailAddress() {
     PropertiesComponent properties = PropertiesComponent.getInstance();
     properties.unsetValue("augment.email.address.storage.v2");
     LOG.info("已清除保存的邮箱地址");
   }

   /**
    * 检查是否有保存的邮箱地址
    *
    * 此方法检查是否存在有效的保存邮箱地址。
    *
    * @return 如果有有效的邮箱地址返回true，否则返回false
    */
   public boolean hasSavedEmailAddress() {
     String emailAddress = getSavedEmailAddress();
     return (emailAddress != null && !emailAddress.trim().isEmpty());
   }

   /**
    * 清除所有保存的数据
    *
    * 此方法清除所有认证相关的保存数据，包括：
    * - 正式验证状态
    * - 授权码
    * - 邮箱地址
    *
    * 使用场景：
    * - 用户手动重置所有认证信息
    * - 插件卸载或重新初始化
    * - 故障排除和调试
    */
   public void clearAllSavedData() {
     clearFormalVerificationStatus();
     clearSavedAuthCode();
     clearSavedEmailAddress();
     LOG.info("已清除所有保存的数据");
   }
 }