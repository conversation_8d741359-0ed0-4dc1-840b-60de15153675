 package com.baaon.v2;

 import com.intellij.openapi.application.Application;
 import com.intellij.openapi.application.ApplicationManager;
 import com.intellij.openapi.diagnostic.Logger;
 import java.lang.reflect.Field;
 import java.util.Map;

/**
 * 不安全的跨插件访问工具类（V2版本） - 简化的反射服务访问
 *
 * 警告：此类包含高风险的反射操作，可能导致系统不稳定
 *
 * 主要功能：
 * 1. 通过反射访问IntelliJ IDEA内部的服务注册表
 * 2. 查找指定类名的服务实例
 * 3. 提供简化的跨插件服务访问接口
 *
 * 与V1版本的区别：
 * - 简化了方法命名（tryGetService vs b1）
 * - 减少了日志输出的详细程度
 * - 保持了核心的反射访问逻辑
 * - 异常处理更加简洁
 *
 * 技术实现：
 * - 使用反射访问ApplicationManager的myServices字段
 * - 遍历服务注册表查找匹配的服务
 * - 通过类名进行服务匹配
 *
 * 安全风险：
 * - 直接操作IntelliJ IDEA的内部实现
 * - 可能因IDE版本更新而失效
 * - 反射操作可能导致安全异常
 * - 违反插件间的隔离原则
 *
 * 使用场景：
 * - 需要访问其他插件服务的场景
 * - 标准API无法满足的深度集成需求
 * - 调试和问题排查
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2.0.0
 */
 public class UnsafeCrossPluginAccess
 {
   /** 日志记录器，用于记录跨插件访问操作和异常信息 */
   private static final Logger LOG = Logger.getInstance(UnsafeCrossPluginAccess.class);




   /**
    * 尝试获取指定的服务实例（简化版本）
    *
    * 此方法通过反射访问IntelliJ IDEA的内部服务注册表，
    * 查找并返回指定类名的服务实例。
    *
    * 功能特点：
    * - 简化的API接口，易于使用
    * - 通过类名匹配查找服务
    * - 基本的异常处理和日志记录
    * - 失败时返回null，不中断程序执行
    *
    * 实现逻辑：
    * 1. 获取Application实例
    * 2. 反射访问myServices字段
    * 3. 遍历服务注册表
    * 4. 匹配服务类名并返回实例
    *
    * 风险提示：
    * - 使用反射访问IDE内部实现
    * - 可能因IDE版本更新而失效
    * - 异常处理相对简单
    *
    * @param pluginId 插件ID（用于标识，当前版本未使用）
    * @param serviceClassName 要查找的服务类的完整类名
    * @return 找到的服务实例，未找到或异常时返回null
    */
   public static Object tryGetService(String pluginId, String serviceClassName) {
     try {
       // 获取IntelliJ IDEA的Application实例
       Application app = ApplicationManager.getApplication();

       // 通过反射获取内部服务注册表字段
       Field servicesField = app.getClass().getDeclaredField("myServices");
       servicesField.setAccessible(true);

       // 获取服务注册表Map
       Map<?, ?> services = (Map<?, ?>)servicesField.get(app);

       // 遍历所有注册的服务
       for (Map.Entry<?, ?> entry : services.entrySet()) {
         Object service = entry.getValue();

         // 获取服务类名并记录日志
         String serviceClass = (service != null) ? service.getClass().getName() : "none";
         LOG.info("services: " + serviceClass);

         // 检查是否匹配目标服务类名
         if (service != null && service.getClass().getName().equals(serviceClassName)) {
           return service;
         }
       }
     } catch (Exception e) {
       // 简化的异常处理
       LOG.error("获取Service异常");
       e.printStackTrace();
     }

     // 未找到服务或发生异常时返回null
     return null;
   }
 }