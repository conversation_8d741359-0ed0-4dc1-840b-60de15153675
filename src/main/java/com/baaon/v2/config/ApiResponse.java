 package com.baaon.v2.config;
 
 public class ApiResponse<T> {
   private boolean success;
   private T data;
   private String message;
   private String errorCode;
   
   public ApiResponse(boolean success, T data, String message) {
     this.success = success;
     this.data = data;
     this.message = message;
   }
   
   public ApiResponse(boolean success, String message, String errorCode) {
     this.success = success;
     this.message = message;
     this.errorCode = errorCode;
   }
   
   public static <T> ApiResponse<T> success(T data) {
     return new ApiResponse<>(true, data, "操作成功");
   }
   
   public static <T> ApiResponse<T> success(T data, String message) {
     return new ApiResponse<>(true, data, message);
   }
   
   public static <T> ApiResponse<T> failure(String message) {
     return new ApiResponse<>(false, message, (String)null);
   }
   
   public static <T> ApiResponse<T> failure(String message, String errorCode) {
     return new ApiResponse<>(false, message, errorCode);
   }
   
   public boolean isSuccess() {
     return this.success;
   }
   
   public void setSuccess(boolean success) {
     this.success = success;
   }
   
   public T getData() {
     return this.data;
   }
   
   public void setData(T data) {
     this.data = data;
   }
   
   public String getMessage() {
     return this.message;
   }
   
   public void setMessage(String message) {
     this.message = message;
   }
   
   public String getErrorCode() {
     return this.errorCode;
   }
   
   public void setErrorCode(String errorCode) {
     this.errorCode = errorCode;
   }
   
   public String toString() {
     boolean var10000 = this.success;
     return "ApiResponse{success=" + var10000 + ", data=" + String.valueOf(this.data) + ", message='" + this.message + "', errorCode='" + this.errorCode + "'}";
   }
 }