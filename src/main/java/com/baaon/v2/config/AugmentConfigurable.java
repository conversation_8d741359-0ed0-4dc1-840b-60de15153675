package com.baaon.v2.config;

import com.intellij.openapi.options.Configurable;
import com.intellij.openapi.options.ConfigurationException;
import com.intellij.openapi.util.NlsContexts.ConfigurableName;
import javax.swing.JComponent;
import org.jetbrains.annotations.Nls;
import org.jetbrains.annotations.Nullable;

/**
 * Augment配置管理器 - IntelliJ IDEA设置界面的配置入口
 *
 * 功能说明：
 * 1. 实现Configurable接口，集成到IntelliJ IDEA的设置界面
 * 2. 管理Augment Assistant Enhanced插件的配置面板
 * 3. 处理配置的创建、修改检测、应用和重置操作
 *
 * 配置界面位置：
 * - File -> Settings -> Tools -> Augment Assistant Enhanced
 *
 * <AUTHOR>
 * @version 2.0.0
 */
public class AugmentConfigurable implements Configurable {

    /** 配置面板实例，延迟初始化以提高性能 */
    private AugmentConfigPanel configPanel;

    @Nls(capitalization = Nls.Capitalization.Title)
    @ConfigurableName
    public String getDisplayName() {
        return "Augment Assistant Enhanced";
    }

    @Nullable
    public JComponent createComponent() {
        if (this.configPanel == null) {
            this.configPanel = new AugmentConfigPanel();
        }

        return this.configPanel.getMainPanel();
    }

    public boolean isModified() {
        return (this.configPanel != null && this.configPanel.isModified());
    }

    public void apply() throws ConfigurationException {
        if (this.configPanel != null) {
            this.configPanel.apply();
        }
    }

    public void reset() {
        // 重置配置到默认状态
        if (this.configPanel != null) {
            // 可以在这里添加重置逻辑
        }
    }

    public void disposeUIResources() {
        this.configPanel = null;
    }
}
