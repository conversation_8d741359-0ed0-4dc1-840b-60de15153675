 package com.baaon.v2.config;
 
 import com.baaon.v2.AuthenticationManager;
 import com.baaon.v2.SessionId;
 import com.baaon.v2.SessionIdReplacer;
 import com.baaon.v2.TrialSessionManager;
 import com.intellij.openapi.diagnostic.Logger;
 import com.intellij.openapi.ui.Messages;
 import com.intellij.ui.components.JBLabel;
 import com.intellij.ui.components.JBTextField;
 import com.intellij.util.ui.FormBuilder;
 import com.intellij.util.ui.JBUI;
 import java.awt.Color;
 import java.awt.Component;
 import java.awt.Desktop;
 import java.awt.FlowLayout;
 import java.awt.Font;
 import java.awt.Toolkit;
 import java.awt.datatransfer.ClipboardOwner;
 import java.awt.datatransfer.StringSelection;
 import java.awt.event.ActionEvent;
 import java.awt.event.ActionListener;
 import java.net.URI;
 import java.util.concurrent.Executors;
 import java.util.concurrent.ScheduledExecutorService;
 import java.util.concurrent.TimeUnit;
 import javax.swing.JButton;
 import javax.swing.JComponent;
 import javax.swing.JPanel;
 import javax.swing.SwingUtilities;
 import javax.swing.Timer;
 
 public class AugmentConfigPanel {
   private static final Logger LOG = Logger.getInstance(AugmentConfigPanel.class);
   private JPanel mainPanel;
   private JBTextField sessionIdField;
   private JBLabel sessionIdSourceLabel;
   private JButton trialActivateButton;
   private JBLabel trialStatusLabel;
   private JPanel trialPanel;
   private JBTextField formalCodeField;
   private JButton formalVerifyButton;
   private JButton formalHelpButton;
   private JButton formalTutorialButton;
   private JBLabel formalStatusLabel;
   private JButton generateButton;
   private JButton copyButton;
   private JButton debugClearButton;
   private JBTextField emailField;
   private JButton generateEmailButton;
   private JButton copyEmailButton;
   private JButton queryCodeButton;
   private JButton copyCodeButton;
   private JBLabel emailStatusLabel;
   private JBLabel codeStatusLabel;
   private String originalSessionId;
   private boolean modified = false;
   private AuthenticationManager authManager = AuthenticationManager.getInstance();
   private TrialSessionManager trialManager = TrialSessionManager.getInstance();
   private EmailVerificationApiService apiService = new EmailVerificationApiService();
   private ScheduledExecutorService statusUpdateScheduler;
   private volatile boolean isDisposed = false;
   private long lastQueryTime = 0L;
   private static final int QUERY_INTERVAL_SECONDS = 5;
   private Timer queryTimer;
   
   public AugmentConfigPanel() {
     try {
       LOG.info("开始初始化 AugmentConfigPanel");

       // 立即初始化UI组件，确保界面可用
       initializeComponents();
       setupLayout();
       setupEventHandlers();

       // 设置初始状态，显示"加载中..."
       setLoadingState();

       LOG.info("AugmentConfigPanel UI初始化完成，开始后台数据加载");

       // 在后台线程中加载数据，避免阻塞UI
       SwingUtilities.invokeLater(() -> {
         try {
           loadAuthenticationStatus();
           loadCurrentSettings();
           startStatusUpdateScheduler();
           LOG.info("AugmentConfigPanel 数据加载完成");
         } catch (Exception e) {
           LOG.error("后台加载配置数据失败", e);
           // 设置错误状态
           setErrorState(e.getMessage());
         }
       });
     } catch (Exception e) {
       LOG.error("AugmentConfigPanel 初始化失败", e);
       // 设置错误状态
       setErrorState(e.getMessage());
     }
   }

   /**
    * 设置加载中状态
    */
   private void setLoadingState() {
     try {
       // 设置SessionId显示为加载中
       if (this.sessionIdField != null) {
         this.sessionIdField.setText("加载中...");
       }
       if (this.sessionIdSourceLabel != null) {
         this.sessionIdSourceLabel.setText("来源: 正在加载...");
       }

       // 设置认证状态为加载中
       if (this.trialStatusLabel != null) {
         this.trialStatusLabel.setText("正在加载认证状态...");
         this.trialStatusLabel.setForeground(Color.GRAY);
       }
       if (this.formalStatusLabel != null) {
         this.formalStatusLabel.setText("正在加载认证状态...");
         this.formalStatusLabel.setForeground(Color.GRAY);
       }

       // 暂时禁用按钮，防止在加载期间操作
       if (this.trialActivateButton != null) {
         this.trialActivateButton.setEnabled(false);
         this.trialActivateButton.setText("加载中...");
       }
       if (this.formalVerifyButton != null) {
         this.formalVerifyButton.setEnabled(false);
         this.formalVerifyButton.setText("加载中...");
       }

       LOG.info("已设置加载中状态");
     } catch (Exception e) {
       LOG.error("设置加载中状态失败", e);
     }
   }

   /**
    * 设置错误状态
    */
   private void setErrorState(String errorMessage) {
     try {
       // 设置SessionId显示为错误
       if (this.sessionIdField != null) {
         this.sessionIdField.setText("加载失败");
       }
       if (this.sessionIdSourceLabel != null) {
         this.sessionIdSourceLabel.setText("来源: 加载失败");
       }

       // 设置认证状态为错误
       if (this.trialStatusLabel != null) {
         this.trialStatusLabel.setText("加载失败: " + errorMessage);
         this.trialStatusLabel.setForeground(Color.RED);
       }
       if (this.formalStatusLabel != null) {
         this.formalStatusLabel.setText("加载失败，请重试");
         this.formalStatusLabel.setForeground(Color.RED);
       }

       // 恢复按钮可用状态
       if (this.trialActivateButton != null) {
         this.trialActivateButton.setEnabled(true);
         this.trialActivateButton.setText("激活3天试用");
       }
       if (this.formalVerifyButton != null) {
         this.formalVerifyButton.setEnabled(true);
         this.formalVerifyButton.setText("正式验证");
       }

       LOG.error("已设置错误状态: " + errorMessage);
     } catch (Exception e) {
       LOG.error("设置错误状态失败", e);
     }
   }

   /**
    * 设置默认UI状态，用于初始化失败时确保界面可用
    */
   private void setDefaultUIState() {
     setLoadingState(); // 使用加载状态作为默认状态
   }

   private void initializeComponents() {
     this.trialActivateButton = new JButton("激活3天试用");
     this.trialActivateButton.setToolTipText("点击激活3天试用期（验证码：1024）");
     this.trialStatusLabel = new JBLabel("点击按钮激活3天试用期");
     this.trialStatusLabel.setFont(this.trialStatusLabel.getFont().deriveFont(2));
     this.trialStatusLabel.setForeground(Color.ORANGE);
     this.formalCodeField = new JBTextField();
     this.formalCodeField.setColumns(15);
     this.formalCodeField.setToolTipText("输入正式验证码，永久解锁功能");
     this.formalVerifyButton = new JButton("正式验证");
     this.formalHelpButton = new JButton("如何获取正式验证码？");
     this.formalHelpButton.setBackground(new Color(76, 175, 80));
     this.formalHelpButton.setForeground(Color.WHITE);
     this.formalHelpButton.setToolTipText("点击查看如何获取正式验证码的详细说明");
     this.formalTutorialButton = new JButton("使用教程");
     this.formalTutorialButton.setBackground(new Color(255, 193, 7));
     this.formalTutorialButton.setForeground(Color.BLACK);
     this.formalTutorialButton.setToolTipText("点击查看详细的使用教程");
     this.formalStatusLabel = new JBLabel("输入正式验证码永久解锁功能");
     this.formalStatusLabel.setFont(this.formalStatusLabel.getFont().deriveFont(2));
     this.formalStatusLabel.setForeground(Color.ORANGE);
     this.sessionIdField = new JBTextField();
     this.sessionIdField.setEditable(false);
     this.sessionIdField.setFont(new Font("Monospaced", 0, 12));
     this.sessionIdSourceLabel = new JBLabel();
     this.sessionIdSourceLabel.setFont(this.sessionIdSourceLabel.getFont().deriveFont(2));
     this.generateButton = new JButton("生成新的SessionId");
     this.copyButton = new JButton("复制到剪贴板");
     this.emailField = new JBTextField();
     this.emailField.setColumns(25);
     this.emailField.setEditable(false);
     this.emailField.setFont(new Font("Monospaced", 0, 12));
     this.emailField.setToolTipText("自动生成的邮箱地址，用于接收验证码");
     this.generateEmailButton = new JButton("生成邮箱");
     this.generateEmailButton.setToolTipText("点击生成一个临时邮箱地址");
     this.copyEmailButton = new JButton("复制邮箱");
     this.copyEmailButton.setToolTipText("复制邮箱地址到剪贴板");
     this.copyEmailButton.setEnabled(false);
     this.queryCodeButton = new JButton("查询验证码");
     this.queryCodeButton.setToolTipText("查询邮箱中最新的验证码（需要约30秒时间，点击间隔30秒）");
     this.queryCodeButton.setEnabled(false);
     this.copyCodeButton = new JButton("复制验证码");
     this.copyCodeButton.setToolTipText("复制验证码到剪贴板");
     this.copyCodeButton.setEnabled(false);
     this.emailStatusLabel = new JBLabel("输入正式验证码后可生成邮箱");
     this.emailStatusLabel.setFont(this.emailStatusLabel.getFont().deriveFont(2));
     this.emailStatusLabel.setForeground(Color.ORANGE);
     this.codeStatusLabel = new JBLabel("生成邮箱后可查询验证码");
     this.codeStatusLabel.setFont(this.codeStatusLabel.getFont().deriveFont(2));
     this.codeStatusLabel.setForeground(Color.GRAY);
     this.debugClearButton = new JButton("清除所有认证状态(调试)");
     this.debugClearButton.setToolTipText("清除所有试用和正式验证状态，仅用于调试");
     this.debugClearButton.setForeground(Color.RED);
     this.debugClearButton.setVisible(false);
     updateButtonStates();
   }
   
   private void setupLayout() {
     this.trialPanel = new JPanel(new FlowLayout(0, 5, 0));
     this.trialPanel.add(this.trialActivateButton);
     JPanel formalPanel = new JPanel(new FlowLayout(0, 5, 0));
     formalPanel.add((Component)this.formalCodeField);
     formalPanel.add(this.formalVerifyButton);
     formalPanel.add(this.formalHelpButton);
     formalPanel.add(this.formalTutorialButton);
     JPanel buttonPanel = new JPanel(new FlowLayout(0, 5, 0));
     buttonPanel.add(this.generateButton);
     buttonPanel.add(this.copyButton);
     JPanel emailPanel = new JPanel(new FlowLayout(0, 5, 0));
     emailPanel.add(this.generateEmailButton);
     emailPanel.add((Component)this.emailField);
     emailPanel.add(this.copyEmailButton);
     JPanel codePanel = new JPanel(new FlowLayout(0, 5, 0));
     codePanel.add(this.queryCodeButton);
     codePanel.add(this.copyCodeButton);
     JPanel debugPanel = new JPanel(new FlowLayout(0, 5, 0));
     debugPanel.add(this.debugClearButton);
     FormBuilder formBuilder = FormBuilder.createFormBuilder();
     formBuilder.addLabeledComponent("试用激活:", this.trialPanel, 1, false).addComponentToRightColumn((JComponent)this.trialStatusLabel, 1).addSeparator().addLabeledComponent("正式验证码:", formalPanel, 1, false).addComponentToRightColumn((JComponent)this.formalStatusLabel, 1).addSeparator();
     formBuilder.addLabeledComponent("当前SessionId:", (JComponent)this.sessionIdField, 1, false).addComponentToRightColumn((JComponent)this.sessionIdSourceLabel, 1).addComponent(buttonPanel, 1).addSeparator().addLabeledComponent("生成邮箱:", emailPanel, 1, false).addComponentToRightColumn((JComponent)this.emailStatusLabel, 1).addLabeledComponent("查询验证码:", codePanel, 1, false).addComponentToRightColumn((JComponent)this.codeStatusLabel, 1).addSeparator().addLabeledComponent("调试功能:", debugPanel, 1, false).addComponentFillVertically(new JPanel(), 0);
     this.mainPanel = formBuilder.getPanel();
     this.mainPanel.setBorder(JBUI.Borders.empty(10));
     updateTrialSectionVisibility();
   }
   
   private void setupEventHandlers() {
     this.trialActivateButton.addActionListener(e -> activateTrialMode());
     this.formalVerifyButton.addActionListener(e -> verifyFormalCode());
     this.formalCodeField.addActionListener(e -> verifyFormalCode());
     this.formalHelpButton.addActionListener(e -> openFormalCodeHelpPage());
     this.formalTutorialButton.addActionListener(e -> openTutorialPage());
     this.generateButton.addActionListener(e -> {
           if (this.authManager.hasValidAuthentication()) {
             generateNewSessionId();
           } else {
             showAuthenticationRequiredMessage();
           } 
         });
     
     this.copyButton.addActionListener(e -> {
           if (this.authManager.hasValidAuthentication()) {
             copySessionIdToClipboard();
           } else {
             showAuthenticationRequiredMessage();
           } 
         });
     
     this.generateEmailButton.addActionListener(e -> generateEmail());
     this.copyEmailButton.addActionListener(e -> copyEmailToClipboard());
     this.queryCodeButton.addActionListener(e -> queryVerificationCode());
     this.copyCodeButton.addActionListener(e -> copyCodeToClipboard());
     this.debugClearButton.addActionListener(e -> {
           int result = Messages.showYesNoDialog("确定要清除所有认证状态吗？\n\n这将清除：\n• 试用验证状态和剩余时间\n• 正式验证状态\n• 所有相关的SessionId数据\n\n此操作不可撤销！", "确认清除认证状态", "确定清除", "取消", Messages.getWarningIcon());
           if (result == 0) {
             clearAllAuthenticationStatus();
             Messages.showInfoMessage("所有认证状态已清除！\n\n现在可以重新激活试用或进行正式验证。", "清除成功");
           } 
         });
   }
 
   
   private void loadCurrentSettings() {
     try {
       // 使用try-catch包装每个可能失败的操作，确保UI不会卡住
       String currentSessionId = "加载中...";
       String source = "Unknown";

       try {
         // 在后台线程中获取SessionId，避免阻塞UI线程
         java.util.concurrent.Future<String[]> future = java.util.concurrent.Executors.newSingleThreadExecutor().submit(() -> {
           try {
             SessionId sessionIdInstance = SessionId.INSTANCE;
             String sessionId = sessionIdInstance.getSessionId();
             String sessionSource = sessionIdInstance.getSessionIdSource();
             return new String[]{sessionId, sessionSource};
           } catch (Exception e) {
             LOG.error("后台获取SessionId失败", e);
             throw e;
           }
         });

         try {
           // 设置5秒超时，避免无限等待
           String[] result = future.get(5, TimeUnit.SECONDS);
           currentSessionId = result[0];
           source = result[1];

           // 更新UI
           this.sessionIdField.setText(currentSessionId);
           this.sessionIdSourceLabel.setText("来源: " + getSourceDescription(source));
           this.originalSessionId = currentSessionId;
           this.modified = false;
           LOG.info("加载当前SessionId配置: " + currentSessionId + " (来源: " + source + ")");
         } catch (java.util.concurrent.TimeoutException e) {
           LOG.error("获取SessionId超时", e);
           this.sessionIdField.setText("加载超时");
           this.sessionIdSourceLabel.setText("来源: 超时");
         } finally {
           future.cancel(true); // 取消任务
         }
       } catch (Exception e) {
         LOG.error("获取SessionId失败", e);
         this.sessionIdField.setText("加载失败");
         this.sessionIdSourceLabel.setText("来源: 未知");
       }

       // 单独处理邮箱地址加载，避免一个失败影响另一个
       try {
         loadSavedEmailAddress();
       } catch (Exception e) {
         LOG.error("加载邮箱地址失败", e);
       }
     } catch (Exception e) {
       LOG.error("加载SessionId配置失败", e);
       this.sessionIdField.setText("加载失败");
       this.sessionIdSourceLabel.setText("来源: 未知");
     }
   }
 
   
   private String getSourceDescription(String source) {
     switch (source) {
       case "TrialSession":
         return "试用会话 (剩余 " + this.trialManager.getRemainingDays() + " 天)";
       
       case "PermanentInstallationID":
         return "永久安装ID";
       
       case "PropertiesComponent":
         return "已保存的配置";
       
       case "Generated":
         return "自动生成";
     } 
     
     return source;
   }
 
 
   
   private void loadSavedEmailAddress() {
     try {
       String savedEmail = this.authManager.getSavedEmailAddress();
       if (savedEmail != null && !savedEmail.trim().isEmpty()) {
         this.emailField.setText(savedEmail);
         this.emailStatusLabel.setText("已加载保存的邮箱地址");
         this.emailStatusLabel.setForeground(Color.GREEN);
         this.copyEmailButton.setEnabled(true);
         this.queryCodeButton.setEnabled(true);
         this.codeStatusLabel.setText("可以查询验证码");
         this.codeStatusLabel.setForeground(Color.ORANGE);
         LOG.info("已加载保存的邮箱地址: " + savedEmail);
       } else {
         this.emailField.setText("");
         this.emailStatusLabel.setText("点击生成邮箱按钮获取临时邮箱地址");
         this.emailStatusLabel.setForeground(Color.ORANGE);
         this.copyEmailButton.setEnabled(false);
         this.queryCodeButton.setEnabled(false);
         this.copyCodeButton.setEnabled(false);
         this.codeStatusLabel.setText("生成邮箱后可查询验证码");
         this.codeStatusLabel.setForeground(Color.GRAY);
         LOG.info("未找到保存的邮箱地址，已重置邮箱相关状态");
       } 
     } catch (Exception e) {
       LOG.error("加载保存的邮箱地址失败", e);
       this.emailStatusLabel.setText("加载邮箱地址失败");
       this.emailStatusLabel.setForeground(Color.RED);
     } 
   }
 
   
   private void generateNewSessionId() {
     try {
       String newSessionId = SessionId.INSTANCE.resetSessionId();
       this.sessionIdField.setText(newSessionId);
       this.sessionIdSourceLabel.setText("来源: " + getSourceDescription("PropertiesComponent"));
       this.modified = true;
       LOG.info("生成新的SessionId: " + newSessionId);
       
       try {
         SessionIdReplacer replacer = new SessionIdReplacer();
         boolean success = replacer.replaceSessionIdClass();
         if (success) {
           Messages.showInfoMessage("新的SessionId已生成并立即生效！\n\n" + newSessionId, "SessionId生成成功");
           LOG.info("SessionId替换成功，新SessionId已生效");
         } else {
           Messages.showWarningDialog("新的SessionId已生成并保存，但替换失败。\n\nSessionId: " + newSessionId + "\n\n请重启IDE以确保新SessionId生效。", "SessionId生成成功，但替换失败");
           LOG.warn("SessionId生成成功，但替换失败");
         } 
       } catch (Exception replaceException) {
         LOG.error("调用SessionIdReplacer失败", replaceException);
         Messages.showWarningDialog("新的SessionId已生成并保存，但替换过程出现异常。\n\nSessionId: " + newSessionId + "\n\n请重启IDE以确保新SessionId生效。\n\n错误详情: " + replaceException.getMessage(), "SessionId生成成功，但替换异常");
       } 
     } catch (Exception e) {
       LOG.error("生成SessionId失败", e);
       Messages.showErrorDialog("生成SessionId失败: " + e.getMessage(), "错误");
     } 
   }
 
   
   private void copySessionIdToClipboard() {
     try {
       String sessionId = this.sessionIdField.getText();
       if (sessionId != null && !sessionId.trim().isEmpty()) {
         Toolkit.getDefaultToolkit().getSystemClipboard().setContents(new StringSelection(sessionId), (ClipboardOwner)null);
         Messages.showInfoMessage("SessionId已复制到剪贴板！", "复制成功");
         LOG.info("SessionId已复制到剪贴板");
       } 
     } catch (Exception e) {
       LOG.error("复制SessionId失败", e);
       Messages.showErrorDialog("复制SessionId失败: " + e.getMessage(), "错误");
     } 
   }
 
   
   public JPanel getMainPanel() {
     return this.mainPanel;
   }
   
   public boolean isModified() {
     return this.modified;
   }
   
   public void apply() {
     this.modified = false;
     this.originalSessionId = this.sessionIdField.getText();
     LOG.info("配置已应用");
   }
   
   public void reset() {
     loadCurrentSettings();
     LOG.info("配置已重置");
   }
   
   private void activateTrialMode() {
     this.trialActivateButton.setEnabled(false);
     this.trialActivateButton.setText("激活中...");
     this.trialStatusLabel.setText("正在激活试用模式，请稍候...");
     this.trialStatusLabel.setForeground(Color.BLUE);
     // SwingUtilities.invokeLater(() -> (new Thread(() -> {})).start());
   }
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
   
   private void verifyFormalCode() {
     String inputCode = this.formalCodeField.getText().trim();
     if (inputCode.isEmpty()) {
       Messages.showWarningDialog("请输入正式验证码！", "验证码为空");
     } else {
       this.authManager.saveAuthCode(inputCode);
       LOG.info("已保存授权码用于邮箱验证功能: " + inputCode);
       this.formalVerifyButton.setEnabled(false);
       this.formalVerifyButton.setText("验证中...");
       this.formalStatusLabel.setText("正在验证正式验证码，请稍候...");
       this.formalStatusLabel.setForeground(Color.BLUE);

       // 在后台线程中执行验证
       SwingUtilities.invokeLater(() -> {
         new Thread(() -> {
           try {
             // 调用验证方法（已修改为直接返回true）
             boolean isValid = this.authManager.verifyFormalCode(inputCode);

             // 在EDT线程中更新UI
             SwingUtilities.invokeLater(() -> {
               if (isValid) {
                 // 验证成功
                 this.authManager.saveFormalVerificationStatus(true);
                 this.formalStatusLabel.setText("验证成功！功能已永久解锁");
                 this.formalStatusLabel.setForeground(Color.GREEN);
                 this.formalVerifyButton.setText("已验证");
                 this.formalCodeField.setText("已验证");
                 this.formalCodeField.setEnabled(false);
                 LOG.info("正式验证成功，UI状态已更新");

                 // 更新其他UI状态
                 updateButtonStates();
                 loadAuthenticationStatus();
               } else {
                 // 验证失败（理论上不会发生，因为已修改为直接返回true）
                 this.formalStatusLabel.setText("验证失败，请检查验证码");
                 this.formalStatusLabel.setForeground(Color.RED);
                 this.formalVerifyButton.setText("正式验证");
                 this.formalVerifyButton.setEnabled(true);
                 LOG.warn("正式验证失败");
               }
             });
           } catch (Exception e) {
             LOG.error("正式验证过程中发生异常", e);
             SwingUtilities.invokeLater(() -> {
               this.formalStatusLabel.setText("验证过程中发生错误");
               this.formalStatusLabel.setForeground(Color.RED);
               this.formalVerifyButton.setText("正式验证");
               this.formalVerifyButton.setEnabled(true);
             });
           }
         }).start();
       });
     } 
   }
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
   
   private void showAuthenticationRequiredMessage() {
     Messages.showWarningDialog("请先进行试用验证或正式验证以启用SessionId功能！", "需要认证");
   }
   
   private void openFormalCodeHelpPage() {
     try {
       String helpUrl = "https://docs.qq.com/doc/DZWFuaVdtemZnUUZj";
       if (Desktop.isDesktopSupported()) {
         Desktop desktop = Desktop.getDesktop();
         if (desktop.isSupported(Desktop.Action.BROWSE)) {
           desktop.browse(new URI(helpUrl));
           LOG.info("已打开正式验证码帮助页面: " + helpUrl);
         } else {
           showBrowserNotSupportedMessage(helpUrl);
         } 
       } else {
         showBrowserNotSupportedMessage(helpUrl);
       } 
     } catch (Exception e) {
       LOG.error("打开帮助页面失败", e);
       Messages.showErrorDialog("无法打开帮助页面！\n\n请手动访问以下链接获取正式验证码：\nhttps://docs.qq.com/doc/DZWFuaVdtemZnUUZj\n\n错误详情: " + e.getMessage(), "打开失败");
     } 
   }
 
   
   private void openTutorialPage() {
     try {
       String tutorialUrl = "https://docs.qq.com/doc/DZUtOVm1DZW1kQXFm";
       if (Desktop.isDesktopSupported()) {
         Desktop desktop = Desktop.getDesktop();
         if (desktop.isSupported(Desktop.Action.BROWSE)) {
           desktop.browse(new URI(tutorialUrl));
           LOG.info("已打开使用教程页面: " + tutorialUrl);
         } else {
           showBrowserNotSupportedMessage(tutorialUrl);
         } 
       } else {
         showBrowserNotSupportedMessage(tutorialUrl);
       } 
     } catch (Exception e) {
       LOG.error("打开教程页面失败", e);
       Messages.showErrorDialog("无法打开教程页面！\n\n请手动访问以下链接查看使用教程：\nhttps://docs.qq.com/doc/DZUtOVm1DZW1kQXFm\n\n错误详情: " + e.getMessage(), "打开失败");
     } 
   }
 
   
   private void showBrowserNotSupportedMessage(String url) {
     Messages.showInfoMessage("系统不支持自动打开浏览器！\n\n请手动复制以下链接到浏览器中访问：\n" + url, "手动访问");
     LOG.warn("系统不支持自动打开浏览器，需要手动访问: " + url);
   }
   
   private void updateTrialSectionVisibility() {
     boolean isFormallyVerified = this.authManager.isFormallyVerified();
     if (this.trialPanel != null) {
       this.trialPanel.setVisible(!isFormallyVerified);
       this.trialStatusLabel.setVisible(!isFormallyVerified);
     } 
     
     LOG.info("试用部分可见性已更新: " + (!isFormallyVerified ? "显示" : "隐藏"));
   }
   
   private void updateButtonStates() {
     boolean hasAuth = this.authManager.hasValidAuthentication();
     this.generateButton.setEnabled(hasAuth);
     this.copyButton.setEnabled(hasAuth);
     if (hasAuth) {
       this.generateButton.setToolTipText("生成新的SessionId");
       this.copyButton.setToolTipText("复制SessionId到剪贴板");
     } else {
       this.generateButton.setToolTipText("请先进行试用或正式验证");
       this.copyButton.setToolTipText("请先进行试用或正式验证");
     } 
     
     boolean isFormallyVerified = this.authManager.isFormallyVerified();
     boolean hasTrialCode = this.trialManager.hasTrialCode();
     this.trialActivateButton.setEnabled((!isFormallyVerified && !hasTrialCode));
     this.formalVerifyButton.setEnabled(!isFormallyVerified);
     this.formalCodeField.setEnabled(!isFormallyVerified);
     this.formalHelpButton.setEnabled(true);
     this.formalTutorialButton.setEnabled(true);
   }
   
   private void loadAuthenticationStatus() {
     try {
       // 在后台线程中获取认证状态，避免阻塞UI线程
       java.util.concurrent.Future<Boolean[]> future = java.util.concurrent.Executors.newSingleThreadExecutor().submit(() -> {
         try {
           boolean isFormallyVerified = this.authManager.isFormallyVerified();
           boolean hasValidTrial = this.trialManager.hasValidTrialSession();
           boolean hasTrialCode = this.trialManager.hasTrialCode();
           int remainingDays = hasValidTrial ? this.trialManager.getRemainingDays() : 0;
           return new Boolean[]{isFormallyVerified, hasValidTrial, hasTrialCode, remainingDays > 0};
         } catch (Exception e) {
           LOG.error("后台获取认证状态失败", e);
           throw e;
         }
       });

       try {
         // 设置5秒超时，避免无限等待
         Boolean[] result = future.get(5, TimeUnit.SECONDS);
         boolean isFormallyVerified = result[0];
         boolean hasValidTrial = result[1];
         boolean hasTrialCode = result[2];

         if (isFormallyVerified) {
           this.formalStatusLabel.setText("已正式验证！功能已永久解锁");
           this.formalStatusLabel.setForeground(Color.GREEN);
           this.formalCodeField.setText("已验证");
           this.formalCodeField.setEnabled(false);
           this.formalVerifyButton.setEnabled(false);
           this.formalVerifyButton.setText("已验证");
           this.formalHelpButton.setToolTipText("查看正式验证码获取说明（已验证）");
           this.formalTutorialButton.setToolTipText("查看详细的使用教程（已验证）");
           this.trialActivateButton.setEnabled(false);
           this.trialActivateButton.setText("已正式验证");
           this.trialStatusLabel.setText("已正式验证，试用功能已禁用");
           this.trialStatusLabel.setForeground(Color.GRAY);
           LOG.info("加载已保存的认证状态：已正式验证");
         } else if (hasValidTrial) {
           int remainingDays = this.trialManager.getRemainingDays();
           this.trialStatusLabel.setText("试用已激活，剩余 " + remainingDays + " 天");
           this.trialStatusLabel.setForeground(Color.GREEN);
           this.trialActivateButton.setEnabled(false);
           this.trialActivateButton.setText("已激活");
           this.formalStatusLabel.setText("输入正式验证码永久解锁功能");
           this.formalStatusLabel.setForeground(Color.ORANGE);
           LOG.info("加载已保存的认证状态：试用期内，剩余 " + remainingDays + " 天");
         } else if (hasTrialCode) {
           this.trialStatusLabel.setText("试用期已过期，请使用正式验证码");
           this.trialStatusLabel.setForeground(Color.RED);
           this.trialActivateButton.setEnabled(false);
           this.trialActivateButton.setText("已过期");
           this.formalStatusLabel.setText("输入正式验证码永久解锁功能");
           this.formalStatusLabel.setForeground(Color.ORANGE);
           LOG.info("加载已保存的认证状态：试用期已过期");
         } else {
           this.trialStatusLabel.setText("点击按钮激活3天试用期");
           this.trialStatusLabel.setForeground(Color.ORANGE);
           this.formalStatusLabel.setText("输入正式验证码永久解锁功能");
           this.formalStatusLabel.setForeground(Color.ORANGE);
           LOG.info("加载已保存的认证状态：未认证");
         }

         updateButtonStates();
         updateTrialSectionVisibility();
       } catch (java.util.concurrent.TimeoutException e) {
         LOG.error("获取认证状态超时", e);
         this.trialStatusLabel.setText("加载认证状态超时，请重试");
         this.trialStatusLabel.setForeground(Color.RED);
         this.formalStatusLabel.setText("加载认证状态超时，请重试");
         this.formalStatusLabel.setForeground(Color.RED);
       } finally {
         future.cancel(true); // 取消任务
       }
     } catch (Exception e) {
       LOG.error("加载认证状态失败", e);
       this.trialStatusLabel.setText("加载认证状态失败，请重试");
       this.trialStatusLabel.setForeground(Color.RED);
       this.formalStatusLabel.setText("加载认证状态失败，请重试");
       this.formalStatusLabel.setForeground(Color.RED);
     }
   }
   
   private void refreshUIAfterAuthentication() {
     updateTrialSectionVisibility();
     updateButtonStates();
     loadCurrentSettings();
     this.mainPanel.revalidate();
     this.mainPanel.repaint();
     LOG.info("认证成功后UI状态已刷新");
   }
   
   public void clearAllAuthenticationStatus() {
     this.authManager.clearFormalVerificationStatus();
     this.authManager.clearSavedAuthCode();
     this.trialManager.clearTrialData();
     this.trialActivateButton.setEnabled(true);
     this.trialActivateButton.setText("激活3天试用");
     this.trialStatusLabel.setText("点击按钮激活3天试用期");
     this.trialStatusLabel.setForeground(Color.ORANGE);
     this.formalCodeField.setText("");
     this.formalCodeField.setEnabled(true);
     this.formalVerifyButton.setEnabled(true);
     this.formalVerifyButton.setText("正式验证");
     this.formalHelpButton.setToolTipText("点击查看如何获取正式验证码的详细说明");
     this.formalTutorialButton.setToolTipText("点击查看详细的使用教程");
     this.formalStatusLabel.setText("输入正式验证码永久解锁功能");
     this.formalStatusLabel.setForeground(Color.ORANGE);
     updateButtonStates();
     loadCurrentSettings();
     updateTrialSectionVisibility();
     this.mainPanel.revalidate();
     this.mainPanel.repaint();
     LOG.info("已清除所有认证状态");
   }
   
   private void startStatusUpdateScheduler() {
     try {
       if (this.statusUpdateScheduler == null || this.statusUpdateScheduler.isShutdown()) {
         LOG.info("正在启动后台状态更新调度器...");

         // 创建守护线程执行器
         this.statusUpdateScheduler = Executors.newSingleThreadScheduledExecutor(r -> {
               Thread thread = new Thread(r, "AugmentConfigPanel-StatusUpdater");
               thread.setDaemon(true);
               return thread;
             });

         // 定期执行状态更新
         this.statusUpdateScheduler.scheduleWithFixedDelay(() -> {
               if (!this.isDisposed) {
                 try {
                   // 在EDT线程中更新UI
                   SwingUtilities.invokeLater(() -> {
                     try {
                       // 静默更新认证状态
                       updateAuthenticationStatusSilently();
                       LOG.debug("后台状态更新成功");
                     } catch (Exception e) {
                       LOG.warn("后台UI更新失败", e);
                     }
                   });
                 } catch (Exception e) {
                   LOG.warn("后台状态更新失败", e);
                 }
               }
             }, 5L, 30L, TimeUnit.MINUTES); // 5分钟后开始，每30分钟执行一次

         LOG.info("后台状态更新调度器已启动，每30分钟检查一次");
       } else {
         LOG.info("后台状态更新调度器已经在运行中");
       }
     } catch (Exception e) {
       LOG.error("启动后台状态更新调度器失败", e);
     }
   }
   
   private void updateAuthenticationStatusSilently() {
     try {
       boolean previousAuthState = this.generateButton.isEnabled();
       boolean isFormallyVerified = this.authManager.isFormallyVerified();
       boolean hasValidTrial = this.trialManager.hasValidTrialSession();
       boolean hasTrialCode = this.trialManager.hasTrialCode();
       boolean currentAuthState = this.authManager.hasValidAuthentication();
       if (previousAuthState != currentAuthState) {
         LOG.info("检测到认证状态变化: " + previousAuthState + " -> " + currentAuthState);
         updateButtonStates();
         loadCurrentSettings();
         if (!isFormallyVerified) {
           if (hasValidTrial) {
             int remainingDays = this.trialManager.getRemainingDays();
             this.trialStatusLabel.setText("试用已激活，剩余 " + remainingDays + " 天");
             this.trialStatusLabel.setForeground(Color.GREEN);
           } else if (hasTrialCode && !this.trialStatusLabel.getText().contains("已过期")) {
             this.trialStatusLabel.setText("试用期已过期，请使用正式验证码");
             this.trialStatusLabel.setForeground(Color.RED);
             this.trialActivateButton.setEnabled(false);
             this.trialActivateButton.setText("已过期");
             LOG.info("试用期已过期，UI状态已更新");
           } 
         }
       } 
     } catch (Exception e) {
       LOG.warn("静默更新认证状态失败", e);
     } 
   }
 
   
   private void generateEmail() {
     String authCode = this.authManager.getSavedAuthCode();
     if (authCode != null && !authCode.isEmpty()) {
       this.generateEmailButton.setEnabled(false);
       this.generateEmailButton.setText("生成中...");
       this.emailStatusLabel.setText("正在生成邮箱地址，请稍候...");
       this.emailStatusLabel.setForeground(Color.BLUE);
       (new Thread(() -> {
             ApiResponse<String> response = this.apiService.generateEmail(authCode);
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
             
             // SwingUtilities.invokeLater(() -> {});
           })).start();
     } else {
       Messages.showWarningDialog("请先输入正式验证码！\n\n授权码将自动保存并用于邮箱验证功能。", "未找到授权码");
     } 
   }
   
   private void copyEmailToClipboard() {
     try {
       String email = this.emailField.getText();
       if (email != null && !email.trim().isEmpty()) {
         Toolkit.getDefaultToolkit().getSystemClipboard().setContents(new StringSelection(email), (ClipboardOwner)null);
         Messages.showInfoMessage("邮箱地址已复制到剪贴板！", "复制成功");
         LOG.info("邮箱地址已复制到剪贴板: " + email);
       } 
     } catch (Exception e) {
       LOG.error("复制邮箱地址失败", e);
       Messages.showErrorDialog("复制邮箱地址失败: " + e.getMessage(), "错误");
     } 
   }
 
   
   private void queryVerificationCode() {
     String email = this.emailField.getText().trim();
     String authCode = this.authManager.getSavedAuthCode();
     if (email.isEmpty()) {
       Messages.showWarningDialog("请先生成邮箱地址！", "邮箱地址为空");
     } else if (authCode != null && !authCode.isEmpty()) {
       long currentTime = System.currentTimeMillis();
       long timeSinceLastQuery = (currentTime - this.lastQueryTime) / 1000L;
       if (this.lastQueryTime > 0L && timeSinceLastQuery < 5L) {
         long remainingSeconds = 5L - timeSinceLastQuery;
         Messages.showWarningDialog("请等待 " + remainingSeconds + " 秒后再次查询验证码！\n\n为了避免频繁请求，查询验证码需要间隔 5 秒。", "查询间隔限制");
       } else {
         this.lastQueryTime = currentTime;
         if (this.queryTimer != null) {
           this.queryTimer.stop();
         }
         
         this.queryCodeButton.setEnabled(false);
         this.queryCodeButton.setText("查询中...");
         this.codeStatusLabel.setText("正在查询验证码，大约需要30秒时间，请耐心等待...");
         this.codeStatusLabel.setForeground(Color.BLUE);
         startQueryCountdown();
         (new Thread(() -> {
               ApiResponse<String> response = this.apiService.queryVerificationCode(email, authCode);
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
               
               // SwingUtilities.invokeLater(() -> {});
             })).start();
       } 
     } else {
       Messages.showWarningDialog("请先输入正式验证码！\n\n授权码将自动保存并用于邮箱验证功能。", "未找到授权码");
     } 
   }
   
   private void startQueryCountdown() {
     final int[] countdown = { 5 };
     this.queryTimer = new Timer(1000, new ActionListener() {
           public void actionPerformed(ActionEvent e) {
             countdown[0] = countdown[0] - 1;
             if (countdown[0] > 0) {
               AugmentConfigPanel.this.codeStatusLabel.setText("正在查询验证码，预计还需 " + countdown[0] + " 秒...");
             } else {
               AugmentConfigPanel.this.codeStatusLabel.setText("查询中，请稍候...");
               AugmentConfigPanel.this.queryTimer.stop();
             } 
           }
         });
     
     this.queryTimer.start();
   }
   
   private void startIntervalCountdown() {
     final int[] countdown = { 5 };
     this.queryTimer = new Timer(1000, new ActionListener() {
           public void actionPerformed(ActionEvent e) {
             countdown[0] = countdown[0] - 1;
             if (countdown[0] > 0) {
               AugmentConfigPanel.this.queryCodeButton.setText("等待 " + countdown[0] + " 秒");
               AugmentConfigPanel.this.queryCodeButton.setEnabled(false);
             } else {
               AugmentConfigPanel.this.queryCodeButton.setText("查询验证码");
               AugmentConfigPanel.this.queryCodeButton.setEnabled(true);
               AugmentConfigPanel.this.queryTimer.stop();
             } 
           }
         });
     
     this.queryTimer.start();
   }
   
   private void copyCodeToClipboard() {
     try {
       String codeText = this.codeStatusLabel.getText();
       if (codeText.contains("验证码查询成功: ")) {
         String code = codeText.substring("验证码查询成功: ".length());
         Toolkit.getDefaultToolkit().getSystemClipboard().setContents(new StringSelection(code), (ClipboardOwner)null);
         Messages.showInfoMessage("验证码已复制到剪贴板！\n\n" + code, "复制成功");
         LOG.info("验证码已复制到剪贴板: " + code);
       } else {
         Messages.showWarningDialog("没有可复制的验证码！\n\n请先查询验证码。", "无验证码");
       } 
     } catch (Exception e) {
       LOG.error("复制验证码失败", e);
       Messages.showErrorDialog("复制验证码失败: " + e.getMessage(), "错误");
     } 
   }
 
   
   public void dispose() {
     this.isDisposed = true;
     if (this.queryTimer != null) {
       this.queryTimer.stop();
       LOG.info("查询验证码定时器已停止");
     } 
     
     if (this.statusUpdateScheduler != null && !this.statusUpdateScheduler.isShutdown()) {
       this.statusUpdateScheduler.shutdown();
       
       try {
         if (!this.statusUpdateScheduler.awaitTermination(5L, TimeUnit.SECONDS)) {
           this.statusUpdateScheduler.shutdownNow();
         }
         
         LOG.info("后台状态更新调度器已停止");
       } catch (InterruptedException e) {
         this.statusUpdateScheduler.shutdownNow();
         Thread.currentThread().interrupt();
         LOG.warn("停止后台状态更新调度器时被中断", e);
       } 
     } 
   }
 }