 package com.baaon.v2.config;
 
 import com.baaon.SecuritySHA1Util;
 import com.intellij.openapi.diagnostic.Logger;
 import org.json.JSONException;
 import org.json.JSONObject;
 import java.io.BufferedReader;
 import java.io.InputStreamReader;
 import java.net.HttpURLConnection;
 import java.net.URL;
 import java.net.URLEncoder;
 import java.nio.charset.StandardCharsets;
 
 
 
 
 
 public class EmailVerificationApiService
 {
   private static final Logger LOG = Logger.getInstance(EmailVerificationApiService.class);
   private static final String EMAIL_GENERATION_API_URL = "http://60.204.224.73:82/api/aug/email/generate";
   private static final String CODE_QUERY_API_URL = "http://60.204.224.73:82/api/aug/email/code/latest";
   private static final int CONNECT_TIMEOUT = 10000;
   private static final int READ_TIMEOUT = 10000;
   private static final String USER_AGENT = "Augment-Assistant-Plugin";
   
   public ApiResponse<String> generateEmail(String authCode) {
     if (authCode != null && !authCode.trim().isEmpty()) {
       try {
         ApiResponse<String> var7; long time = System.currentTimeMillis();
         String encode = SecuritySHA1Util.shaEncode("" + time + "10f2f96d89a32941edf01bxcz0a076f10");
         String apiUrl = "http://60.204.224.73:82/api/aug/email/generate?ts=" + time + "&sign=" + encode + "&authCode=" + URLEncoder.encode(authCode, StandardCharsets.UTF_8.toString());
         LOG.info("调用生成邮箱API: " + apiUrl);
         URL url = new URL(apiUrl);
         HttpURLConnection connection = (HttpURLConnection)url.openConnection();
 
         
         try {
           connection.setRequestMethod("POST");
           connection.setConnectTimeout(10000);
           connection.setReadTimeout(10000);
           connection.setRequestProperty("User-Agent", "Augment-Assistant-Plugin");
           connection.setRequestProperty("Accept", "application/json");
           int responseCode = connection.getResponseCode();
           LOG.info("生成邮箱API响应码: " + responseCode);
           if (responseCode != 200) {
             LOG.warn("生成邮箱API调用失败，响应码: " + responseCode);
             ApiResponse<?> var13 = ApiResponse.failure("API调用失败，响应码: " + responseCode);
             return (ApiResponse)var13;
           } 
           
           String responseBody = readResponse(connection);
           LOG.info("生成邮箱API响应: " + responseBody);
           var7 = parseEmailResponse(responseBody);
         } finally {
           connection.disconnect();
         } 
         
         return var7;
       } catch (Exception e) {
         LOG.error("生成邮箱API调用异常", e);
         return ApiResponse.failure("网络请求异常: " + e.getMessage());
       } 
     }
     return ApiResponse.failure("授权码不能为空");
   }
 
   
   public ApiResponse<String> queryVerificationCode(String email, String authCode) {
     if (email != null && !email.trim().isEmpty()) {
       if (authCode != null && !authCode.trim().isEmpty()) {
         try {
           ApiResponse<String> var8; String var10000 = URLEncoder.encode(email, StandardCharsets.UTF_8.toString());
           long time = System.currentTimeMillis();
           String encode = SecuritySHA1Util.shaEncode("" + time + "10f2f96d89a32941edf01bxcz0a076f10");
           
           String apiUrl = "http://60.204.224.73:82/api/aug/email/code/latest?ts=" + time + "&sign=" + encode + "&email=" + var10000 + "&authCode=" + URLEncoder.encode(authCode, StandardCharsets.UTF_8.toString());
           LOG.info("调用查询验证码API: " + apiUrl);
           URL url = new URL(apiUrl);
           HttpURLConnection connection = (HttpURLConnection)url.openConnection();
 
           
           try {
             connection.setRequestMethod("POST");
             connection.setConnectTimeout(10000);
             connection.setReadTimeout(10000);
             connection.setRequestProperty("User-Agent", "Augment-Assistant-Plugin");
             connection.setRequestProperty("Accept", "application/json");
             int responseCode = connection.getResponseCode();
             LOG.info("查询验证码API响应码: " + responseCode);
             if (responseCode != 200) {
               LOG.warn("查询验证码API调用失败，响应码: " + responseCode);
               ApiResponse<?> var14 = ApiResponse.failure("API调用失败，响应码: " + responseCode);
               return (ApiResponse)var14;
             } 
             
             String responseBody = readResponse(connection);
             LOG.info("查询验证码API响应: " + responseBody);
             var8 = parseCodeResponse(responseBody);
           } finally {
             connection.disconnect();
           } 
           
           return var8;
         } catch (Exception e) {
           LOG.error("查询验证码API调用异常", e);
           return ApiResponse.failure("网络请求异常: " + e.getMessage());
         } 
       }
       return ApiResponse.failure("授权码不能为空");
     } 
     
     return ApiResponse.failure("邮箱地址不能为空");
   }
 
   
   private String readResponse(HttpURLConnection connection) throws Exception {
     BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8)); 
     try { StringBuilder response = new StringBuilder();
       
       String line;
       while ((line = reader.readLine()) != null) {
         response.append(line);
       }
       
       String str1 = response.toString();
       reader.close(); return str1; }
     catch (Throwable throwable) { try { reader.close(); }
       catch (Throwable throwable1) { throwable.addSuppressed(throwable1); }
        throw throwable; }
      }

  private ApiResponse<String> parseEmailResponse(String responseBody) {
    try {
      JSONObject jsonResponse = new JSONObject(responseBody);
      if (jsonResponse.optBoolean("success", false)) {
        String data = jsonResponse.optString("data");
        if (data != null && !data.trim().isEmpty()) {
          return ApiResponse.success(data, "邮箱生成成功");
        }
      }

      String message = jsonResponse.optString("message", "邮箱生成失败");
      return ApiResponse.failure(message);
    } catch (JSONException e) {
      LOG.error("解析邮箱响应JSON失败", e);
      return ApiResponse.failure("响应格式错误");
    }
  }
 
   
   private ApiResponse<String> parseCodeResponse(String responseBody) {
     try {
       JSONObject jsonResponse = new JSONObject(responseBody);
       if (jsonResponse.optBoolean("success", false)) {
         String data = jsonResponse.optString("data");
         if (data != null && !data.trim().isEmpty()) {
           return ApiResponse.success(data, "验证码查询成功");
         }
       } else {
         LOG.warn("验证码查询失败，响应: " + responseBody);
         return ApiResponse.failure("验证码查询失败:" + jsonResponse.optString("message"));
       }

       String message = jsonResponse.optString("message", "验证码查询失败");
       return ApiResponse.failure(message);
     } catch (JSONException e) {
       LOG.error("解析验证码响应JSON失败", e);
       return ApiResponse.failure("响应格式错误");
     }
   }
 }