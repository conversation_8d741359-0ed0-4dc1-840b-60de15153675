package com.baaon.v2.config;
/**
 * 域名和API配置常量类 - 存储Augment服务的基础配置信息
 *
 * 主要功能：
 * 1. 定义Augment服务的基础域名地址
 * 2. 存储API访问所需的密钥信息
 * 3. 提供集中化的配置管理
 *
 * 使用场景：
 * - API服务调用时的基础URL构建
 * - 身份验证和授权请求
 * - 服务端点的统一配置
 *
 * 配置说明：
 * - DOMAIN: Augment服务的基础域名，包含协议和端口
 * - API_KEY: API访问密钥，用于服务认证
 *
 * 注意事项：
 * - 生产环境中应考虑将敏感信息外部化
 * - API_KEY应定期更新以确保安全性
 * - 域名变更时需要同步更新此配置
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2.0.0
 */

public class DomainConstant {
  /** Augment服务的基础域名地址，包含协议（http）和端口号（82） */
  public static final String DOMAIN = "http://60.204.224.73:82";

  /** API访问密钥，用于服务端的身份验证和授权 */
  public static final String API_KEY = "10f2f96d89a32941edf01bxcz0a076f10";
}