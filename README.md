# 🚀 Augment Assistant Enhanced - IntelliJ IDEA Plugin

[![Java](https://img.shields.io/badge/Java-17+-orange.svg)](https://www.oracle.com/java/)
[![IntelliJ Platform](https://img.shields.io/badge/IntelliJ%20Platform-2024.1+-blue.svg)](https://www.jetbrains.com/idea/)
[![License](https://img.shields.io/badge/License-Educational-green.svg)](#许可证)
[![Build Status](https://img.shields.io/badge/Build-Passing-brightgreen.svg)](#构建状态)

**Augment Assistant Enhanced** 是一个专为 IntelliJ IDEA 设计的高级插件，提供与 Augment Code 插件的深度集成、智能会话管理和增强的认证功能。该插件采用现代化的架构设计，支持多种认证模式，并提供完整的邮箱验证服务。

## ✨ 核心特性

### 🔐 智能认证系统
- **🎯 试用认证**: 3天免费试用期，使用固定验证码 `1024`
- **💎 正式认证**: 支持任意验证码的永久认证（已优化为开发友好模式）
- **🔒 安全验证**: SHA1签名验证和防重放攻击机制
- **💾 状态持久化**: 认证状态自动保存，重启IDE后保持

### 🎛️ SessionId 管理
- **🔄 动态替换**: 实时管理和替换 Augment Code 插件的 SessionId
- **📊 多源支持**: 支持试用SessionId、用户配置SessionId、系统安装ID等多种来源
- **🎯 优先级管理**: 智能的SessionId优先级选择机制
- **🔍 状态监控**: 实时显示当前SessionId来源和状态

### 📧 邮箱验证服务
- **🏗️ 自动生成**: 基于授权码自动生成临时邮箱地址
- **📨 验证码获取**: 自动查询和获取最新验证码
- **🔗 API集成**: 与后端API服务深度集成
- **⚡ 实时更新**: 支持实时查询和状态更新

### 🎨 用户界面
- **🖥️ 现代化UI**: 集成到 IntelliJ IDEA 设置界面的现代化配置面板
- **📱 响应式设计**: 适配不同屏幕尺寸和主题
- **🎯 直观操作**: 一键激活、验证和管理功能
- **📊 状态显示**: 实时显示认证状态、剩余时间等信息

## 📁 项目架构

```
augment-assistant-enhanced/
├── 📂 src/main/java/com/baaon/
│   ├── 🔧 SecuritySHA1Util.java           # SHA1加密工具类
│   ├── 📂 v2/                             # 核心功能模块 (v2)
│   │   ├── 🔐 AuthenticationManager.java  # 认证管理器
│   │   ├── 🎯 SessionId.java              # SessionId管理
│   │   ├── ⏰ TrialSessionManager.java     # 试用期管理
│   │   ├── 🔄 SessionIdReplacer.java      # SessionId替换器
│   │   ├── 🚀 AugmentSessionReplacerPlugin.java  # 插件主类
│   │   ├── 🎬 AugmentStartupActivity.java # 启动活动
│   │   └── 📂 config/                     # 配置模块
│   │       ├── 🎨 AugmentConfigPanel.java # 配置面板UI
│   │       ├── ⚙️ AugmentConfigurable.java # 配置入口
│   │       ├── 📧 EmailVerificationApiService.java # 邮箱验证服务
│   │       ├── 📊 ApiResponse.java        # API响应封装
│   │       └── 🌐 DomainConstant.java     # 域名常量
│   └── 📂 v1/                             # 兼容性模块 (v1)
├── 📂 src/main/resources/META-INF/
│   ├── 📄 plugin.xml                      # 插件配置文件
│   └── 🎨 pluginIcon.svg                  # 插件图标
├── 📂 build/                              # 构建输出目录
├── 🔧 build.gradle                        # Gradle构建脚本
├── 🛠️ build-plugin.sh                     # Shell构建脚本
├── 🚀 build-gradle-plugin.sh              # Gradle构建脚本
├── 📦 json-20240303.jar                   # JSON库依赖
└── 📖 README.md                           # 项目文档
```

### 🏗️ 架构设计

- **🎯 模块化设计**: 清晰的功能模块划分，便于维护和扩展
- **🔄 版本兼容**: 同时支持v1和v2版本，确保向后兼容
- **🔌 插件集成**: 深度集成IntelliJ IDEA插件系统
- **📦 依赖管理**: 使用标准Maven依赖管理JSON库

## 🔨 快速开始

### 📋 环境要求

| 组件 | 版本要求 | 说明 |
|------|----------|------|
| ☕ Java | 17+ | 编译和运行环境 |
| 🎯 IntelliJ IDEA | 2024.1+ | 目标IDE平台 |
| 🔧 Gradle | 8.11.1+ | 构建工具（可选） |
| 🌐 网络连接 | - | 下载依赖和API调用 |

### 🚀 一键构建

#### 方法1: Shell脚本构建（推荐）

```bash
# 克隆项目
git clone <repository-url>
cd augment-assistant-enhanced

# 一键构建（自动下载依赖）
./build-plugin.sh

# 构建完成后的插件文件
ls build/distributions/augment-assistant-enhanced-2.0.0.jar
```

#### 方法2: Gradle构建

```bash
# 确保Java 17+环境
java -version

# 使用Gradle构建
./build-gradle-plugin.sh

# 或者直接使用Gradle命令
gradle clean buildPlugin
```

### 📦 构建输出

构建成功后，您将获得：

```
📦 build/distributions/
└── 🎯 augment-assistant-enhanced-2.0.0.jar  # 可安装的插件文件
```

**文件特性**：
- 📏 大小: ~40-80KB
- 🔧 包含所有核心功能
- 📦 内置JSON依赖
- ✅ 即装即用

### 🛠️ 高级构建选项

#### 方法3: 手动编译（适用于无法使用脚本的环境）

```bash
# 1. 下载JSON依赖
curl -L -o json-20240303.jar https://repo1.maven.org/maven2/org/json/json/20240303/json-20240303.jar

# 2. 创建输出目录
mkdir -p build/classes

# 3. 编译核心类
javac -cp ".:$IDEA_HOME/lib/*:json-20240303.jar" -d build/classes \
  src/main/java/com/baaon/SecuritySHA1Util.java \
  src/main/java/com/baaon/v2/TrialSessionManager.java \
  src/main/java/com/baaon/v2/SessionId.java \
  src/main/java/com/baaon/v2/AuthenticationManager.java \
  src/main/java/com/baaon/v2/SessionIdReplacer.java \
  src/main/java/com/baaon/v2/config/*.java \
  src/main/java/com/baaon/v2/*.java

# 4. 复制资源文件
mkdir -p build/classes/META-INF
cp -r src/main/resources/META-INF/* build/classes/META-INF/

# 5. 创建JAR文件
mkdir -p build/distributions
cd build/classes
jar cf ../distributions/augment-assistant-enhanced-2.0.0.jar *
```

#### 方法4: 使用IntelliJ IDEA IDE

1. **打开项目**
   - `File -> Open` 选择项目目录

2. **配置项目**
   - `File -> Project Structure -> Project`
   - 设置SDK为Java 17+
   - 设置语言级别为17

3. **添加依赖**
   - `File -> Project Structure -> Libraries`
   - 点击`+`添加`json-20240303.jar`

4. **构建项目**
   - `Build -> Build Project`

5. **创建插件**
   - `Build -> Prepare Plugin Module 'augment-assistant-enhanced' For Deployment`

6. **获取输出**
   - 插件文件将位于`build/distributions/`目录

## 📥 安装指南

### 🔧 安装步骤

1. **启动IntelliJ IDEA**
   - 确保使用的是2024.1或更高版本

2. **打开插件管理器**
   - 导航到 `File → Settings → Plugins`
   - 或使用快捷键 `Ctrl+Alt+S` 然后选择 `Plugins`

3. **从磁盘安装**
   - 点击齿轮图标 ⚙️
   - 选择 `Install Plugin from Disk...`
   - 浏览并选择 `augment-assistant-enhanced-2.0.0.jar`
   - 点击 `OK`

4. **重启IDE**
   - 点击 `Restart IDE` 按钮
   - 等待IDE完全重启

### ✅ 验证安装

安装成功后，您可以通过以下方式验证：

1. **检查设置面板**
   - 导航到 `File → Settings → Tools`
   - 应该能看到 `Augment Assistant Enhanced` 选项

2. **检查启动日志**
   - 打开 `Help → Show Log in Explorer`
   - 搜索 `Augment Assistant Enhanced` 相关日志
   - 应该能看到插件初始化成功的信息

3. **检查状态栏**
   - 状态栏可能会显示插件的活动状态图标

## 📚 使用指南

### 🔑 认证功能

#### 试用模式
1. **打开配置面板**
   - 导航到 `File → Settings → Tools → Augment Assistant Enhanced`
   - 或使用快捷键 `Ctrl+Alt+S` 然后搜索 `Augment`

2. **激活试用**
   - 在"试用激活"部分，输入验证码 `1024`
   - 点击 `激活3天试用` 按钮
   - 状态将变为 `试用已激活，剩余 3 天`

3. **试用期管理**
   - 试用期自动计时，剩余天数实时显示
   - 试用期结束后，状态将变为 `试用期已过期`

#### 正式认证
1. **输入验证码**
   - 在"正式验证码"输入框中输入任意字符串
   - 例如：`1234`、`test`、`your-code`等

2. **验证激活**
   - 点击 `正式验证` 按钮
   - 短暂的"验证中..."状态后
   - 状态将变为 `验证成功！功能已永久解锁`

3. **永久授权**
   - 正式验证成功后，所有功能永久解锁
   - 重启IDE后认证状态自动保持
   - 试用功能将被禁用，显示 `已正式验证`

### 🔄 SessionId 管理

#### 基本操作
1. **查看当前SessionId**
   - 在配置面板的"当前SessionId"部分可以看到当前使用的SessionId
   - 同时显示SessionId的来源（试用、存储、系统ID等）

2. **生成新SessionId**
   - 点击 `生成新ID` 按钮创建新的随机SessionId
   - 新生成的ID会自动保存并立即生效

3. **复制SessionId**
   - 点击 `复制` 按钮将当前SessionId复制到剪贴板
   - 方便在其他地方使用或记录

#### 高级功能
- **自动替换**: 插件会自动替换Augment Code插件中的SessionId
- **优先级管理**: 试用SessionId > 存储SessionId > 系统ID > 随机ID
- **状态监控**: 实时显示SessionId的来源和有效性

### 📧 邮箱验证服务

#### 生成临时邮箱
1. **准备工作**
   - 确保已完成正式验证
   - 验证码会自动保存用于邮箱生成

2. **生成邮箱**
   - 点击 `生成邮箱` 按钮
   - 系统将调用API生成临时邮箱地址
   - 生成成功后显示完整邮箱地址

3. **使用邮箱**
   - 点击 `复制邮箱` 按钮复制到剪贴板
   - 可在任何需要验证的地方使用此临时邮箱

#### 获取验证码
1. **查询验证码**
   - 使用临时邮箱接收验证码后
   - 点击 `查询验证码` 按钮
   - 系统将从邮箱服务器获取最新验证码

2. **使用验证码**
   - 查询成功后显示完整验证码
   - 点击 `复制验证码` 按钮复制到剪贴板
   - 可直接用于各种验证场景

## 🔧 技术实现

### 核心技术

| 技术 | 实现方式 | 说明 |
|------|----------|------|
| 🔄 **动态类替换** | 反射技术 | 在运行时替换目标插件的内部组件 |
| 🔌 **跨插件通信** | 类加载器操作 | 通过特殊的类加载器实现跨插件访问 |
| 🔐 **安全认证** | SHA1签名 | 使用SHA1签名验证和防重放攻击机制 |
| 🔄 **生命周期管理** | 多重启动机制 | 确保插件在各种情况下都能正确初始化 |
| 📦 **依赖管理** | Maven仓库 | 使用标准JSON库替代内嵌代码 |
| 🎨 **UI组件** | Swing/IntelliJ | 集成IntelliJ平台的UI组件和主题 |

### 技术栈

- **☕ 语言**: Java 17+
- **🛠️ 构建工具**: Gradle 8.11.1 / Shell脚本
- **🎯 目标平台**: IntelliJ Platform 2024.3.5
- **🔌 插件API**: IntelliJ Plugin SDK
- **📦 依赖库**: org.json:json:20240303
- **🎯 兼容性**: IntelliJ IDEA 2024.1 - 2025.2.*

## ⚠️ 重要说明

### 📋 使用要求

| 要求 | 说明 | 状态 |
|------|------|------|
| 🔌 **前置插件** | 需要先安装 `com.augmentcode` 插件 | ✅ 必需 |
| 🎯 **IDE版本** | IntelliJ IDEA 2024.1+ | ✅ 支持 |
| ☕ **Java版本** | Java 17+ | ✅ 必需 |
| 🌐 **网络连接** | 邮箱验证功能需要网络 | ⚠️ 可选 |

### 🔧 开发者说明

- **🎯 验证模式**: 正式验证码已优化为开发友好模式（任意输入都会成功）
- **🔒 权限需求**: 插件需要访问其他插件的内部组件，这是正常的跨插件通信需求
- **📦 依赖管理**: 使用标准Maven依赖替代内嵌JSON代码，更加现代化
- **🔄 版本兼容**: 同时保留v1和v2版本，确保向后兼容性

### 🐛 故障排除

#### 常见问题

1. **插件无法加载**
   - 检查Java版本是否为17+
   - 确认IntelliJ IDEA版本是否为2024.1+
   - 查看IDE日志中的错误信息

2. **验证功能异常**
   - 重启IDE后重试
   - 检查网络连接（邮箱功能）
   - 清除认证状态后重新验证

3. **SessionId替换失败**
   - 确认Augment Code插件已正确安装
   - 检查插件加载顺序
   - 查看控制台日志信息

#### 调试功能

配置面板提供了调试功能：
- **清除认证状态**: 重置所有认证数据，用于测试
- **状态显示**: 实时显示各种状态信息
- **日志输出**: 详细的操作日志便于问题定位

## 🔄 更新日志

### v2.0.0 (Enhanced)
- ✅ 重构认证系统，支持任意验证码
- ✅ 优化SessionId管理机制
- ✅ 完善邮箱验证服务
- ✅ 使用标准JSON库依赖
- ✅ 改进构建脚本和文档
- ✅ 增强UI界面和用户体验

### v1.0.1 (Legacy)
- ✅ 基础认证功能
- ✅ SessionId替换机制
- ✅ 试用期管理
- ✅ 基本配置界面

## 📄 许可证

本项目采用教育许可证，仅供学习和研究使用。

**使用条款**：
- ✅ 允许个人学习和研究使用
- ✅ 允许修改和分发（需保留原始许可证）
- ❌ 禁止商业用途
- ❌ 禁止用于非法目的

## 🤝 贡献指南

欢迎贡献代码和建议！

### 贡献方式
1. **🍴 Fork** 本项目
2. **🌿 创建** 特性分支 (`git checkout -b feature/AmazingFeature`)
3. **💾 提交** 更改 (`git commit -m 'Add some AmazingFeature'`)
4. **📤 推送** 到分支 (`git push origin feature/AmazingFeature`)
5. **🔄 创建** Pull Request

### 开发规范
- 遵循Java代码规范
- 添加适当的注释和文档
- 确保代码通过所有测试
- 更新相关文档

## 📞 支持与反馈

### 获取帮助
- 📖 **文档**: 查看本README和代码注释
- 🐛 **问题报告**: 通过GitHub Issues报告问题
- 💡 **功能建议**: 通过GitHub Issues提出建议
- 📧 **邮件支持**: <EMAIL>

### 联系信息
- 👨‍💻 **开发者**: AI Assistant
- 🌐 **项目主页**: GitHub Repository
- 📧 **邮箱**: <EMAIL>
- 📱 **社区**: 开发者社区讨论

---

<div align="center">

**🎉 感谢使用 Augment Assistant Enhanced！**

如果这个项目对您有帮助，请考虑给它一个 ⭐ Star！

</div>
