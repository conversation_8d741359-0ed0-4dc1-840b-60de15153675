plugins {
    id 'java'
    id 'org.jetbrains.intellij' version '1.17.2'
}

group = 'com.baaon'
version = '1.0.1'

repositories {
    mavenCentral()
}

// Configure Gradle IntelliJ Plugin
// Read more: https://plugins.jetbrains.com/docs/intellij/tools-gradle-intellij-plugin.html
intellij {
    version = '2024.3.5'
    type = 'IU' // Target IDE Platform
    
    plugins = [
        // Plugin Dependencies. Uses `platformPlugins` property from the gradle.properties file.
    ]
}

dependencies {
    // JSON库依赖 - 用于邮箱验证API的JSON解析
    implementation 'org.json:json:20240303'

    // Add any additional dependencies here if needed
}

tasks {
    // Set the JVM compatibility versions
    withType(JavaCompile) {
        sourceCompatibility = "17"
        targetCompatibility = "17"
        options.encoding = 'UTF-8'
    }

    patchPluginXml {
        sinceBuild = '241'
        untilBuild = '252.*'
        
        // Extract the <!-- Plugin description --> section from README.md and provide for the plugin's manifest
        pluginDescription = """
        <p>Augment Assistant - IntelliJ IDEA插件助手</p>
        <p>主要功能：</p>
        <ul>
            <li>重置SessionId</li>
            <li>自动生成邮箱地址</li>
            <li>自动获取验证码</li>
            <li>试用和正式认证管理</li>
            <li>与Augment Code插件集成</li>
        </ul>
        """
        
        changeNotes = """
        <h3>Version 1.0.1</h3>
        <ul>
            <li>优化了正式验证码验证逻辑</li>
            <li>改进了SessionId管理机制</li>
            <li>增强了试用认证功能</li>
            <li>修复了已知问题</li>
        </ul>
        """
    }

    signPlugin {
        certificateChain = System.getenv("CERTIFICATE_CHAIN")
        privateKey = System.getenv("PRIVATE_KEY")
        password = System.getenv("PRIVATE_KEY_PASSWORD")
    }

    publishPlugin {
        token = System.getenv("PUBLISH_TOKEN")
    }
    
    buildPlugin {
        archiveFileName = "augment-assistant-${version}.jar"
    }
}

// Configure source sets to match the current directory structure
sourceSets {
    main {
        java {
            srcDirs = ['com']
        }
        resources {
            srcDirs = ['META-INF']
            exclude 'META-INF/maven/**'
            exclude 'META-INF/versions/**'
        }
    }
}

// Ensure proper resource handling
processResources {
    from('META-INF') {
        into 'META-INF'
        exclude 'maven/**'
        exclude 'versions/**'
    }
}
