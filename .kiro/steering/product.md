# Product Overview

**Augment Assistant Enhanced** is an IntelliJ IDEA plugin that provides deep integration with the Augment Code plugin, offering intelligent session management and enhanced authentication features.

## Core Features

- **Smart Authentication System**: 3-day trial mode with fixed verification code `1024` and formal authentication with permanent access
- **SessionId Management**: Dynamic replacement and management of Augment Code plugin SessionIds with priority-based selection
- **Email Verification Service**: Auto-generated temporary email addresses with verification code retrieval
- **Modern UI**: Integrated configuration panel within IntelliJ IDEA settings

## Target Platform

- **IDE**: IntelliJ IDEA 2024.1+
- **Java**: 17+
- **Dependencies**: Requires `com.augmentcode` plugin to be installed first

## Architecture

The plugin uses a modular design with v1 and v2 versions for backward compatibility. It employs reflection and cross-plugin communication to dynamically replace components in the target plugin at runtime.