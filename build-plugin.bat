@echo off
setlocal enabledelayedexpansion

echo 🚀 开始构建 Augment Assistant 插件...

REM 检查 Java 版本
echo 📋 检查 Java 环境...
java -version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到 Java，请安装 Java 17 或更高版本
    pause
    exit /b 1
)

echo ✅ Java 环境检查通过

REM 创建构建目录
echo 📁 创建构建目录...
if exist build rmdir /s /q build
mkdir build\classes
mkdir build\distributions

REM 设置 IntelliJ IDEA 路径（需要根据实际安装路径调整）
set "IDEA_HOME=%IDEA_HOME%"
if "%IDEA_HOME%"=="" (
    echo ⚠️  警告: 未设置 IDEA_HOME 环境变量，尝试常见路径...
    
    REM 尝试常见的 IntelliJ IDEA 安装路径
    set "POSSIBLE_PATHS=C:\Program Files\JetBrains\IntelliJ IDEA 2024.3;C:\Program Files (x86)\JetBrains\IntelliJ IDEA 2024.3;%LOCALAPPDATA%\JetBrains\IntelliJ IDEA 2024.3"
    
    for %%p in (!POSSIBLE_PATHS!) do (
        if exist "%%p" (
            set "IDEA_HOME=%%p"
            echo ✅ 找到 IntelliJ IDEA: !IDEA_HOME!
            goto :found_idea
        )
    )
    
    echo ❌ 错误: 未找到 IntelliJ IDEA 安装目录
    echo 请设置 IDEA_HOME 环境变量指向 IntelliJ IDEA 安装目录
    echo 例如: set IDEA_HOME=C:\Program Files\JetBrains\IntelliJ IDEA 2024.3
    pause
    exit /b 1
)

:found_idea

REM 构建类路径
set "CLASSPATH=.;%IDEA_HOME%\lib\*;%IDEA_HOME%\plugins\platform-api\lib\*"

REM 编译 Java 源文件
echo 🔨 编译 Java 源文件...

REM 创建源文件列表
dir /s /b src\main\java\*.java > sources.txt

REM 检查是否有源文件
for /f %%i in (sources.txt) do (
    set "HAS_SOURCES=1"
    goto :compile
)

echo ❌ 错误: 未找到 Java 源文件
pause
exit /b 1

:compile
javac -cp "%CLASSPATH%" -d build\classes -encoding UTF-8 @sources.txt
if errorlevel 1 (
    echo ❌ 错误: Java 编译失败
    pause
    exit /b 1
)

echo ✅ Java 编译完成
del sources.txt

REM 复制资源文件
echo 📋 复制资源文件...
if exist src\main\resources (
    xcopy /s /e /y src\main\resources\* build\classes\
    echo ✅ 资源文件复制完成
)

REM 创建 JAR 文件
echo 📦 创建插件 JAR 文件...
cd build\classes

jar cf ..\distributions\augment-assistant-1.0.1.jar *
if errorlevel 1 (
    echo ❌ 错误: JAR 文件创建失败
    cd ..\..
    pause
    exit /b 1
)

cd ..\..

REM 验证 JAR 文件
if exist "build\distributions\augment-assistant-1.0.1.jar" (
    echo ✅ 插件构建成功!
    echo 📍 插件文件位置: build\distributions\augment-assistant-1.0.1.jar
    
    REM 显示文件大小
    for %%A in (build\distributions\augment-assistant-1.0.1.jar) do (
        echo 📊 文件大小: %%~zA 字节
    )
    
    echo.
    echo 🎉 构建完成! 现在可以安装插件了:
    echo    1. 打开 IntelliJ IDEA
    echo    2. File -^> Settings -^> Plugins
    echo    3. 点击齿轮图标 -^> Install Plugin from Disk...
    echo    4. 选择: %CD%\build\distributions\augment-assistant-1.0.1.jar
    echo    5. 重启 IntelliJ IDEA
    
) else (
    echo ❌ 错误: 插件构建失败
    pause
    exit /b 1
)

pause
