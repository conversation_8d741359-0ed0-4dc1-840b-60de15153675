#!/bin/bash

# Augment Assistant Plugin Build Script
# 用于将反编译的项目重新打包为 IntelliJ IDEA 插件

set -e

echo "🚀 开始构建 Augment Assistant 插件..."

# 检查 Java 版本
echo "📋 检查 Java 环境..."
if ! command -v java &> /dev/null; then
    echo "❌ 错误: 未找到 Java，请安装 Java 17 或更高版本"
    exit 1
fi

JAVA_VERSION=$(java -version 2>&1 | head -n1 | cut -d'"' -f2 | cut -d'.' -f1)
if [ "$JAVA_VERSION" -lt 17 ]; then
    echo "❌ 错误: Java 版本过低，需要 Java 17 或更高版本，当前版本: $JAVA_VERSION"
    exit 1
fi

echo "✅ Java 版本检查通过: $JAVA_VERSION"

# 创建构建目录
echo "📁 创建构建目录..."
rm -rf build
mkdir -p build/classes
mkdir -p build/distributions

# 设置类路径（需要根据实际 IntelliJ IDEA 安装路径调整）
IDEA_HOME=${IDEA_HOME:-"/Applications/IntelliJ IDEA.app/Contents"}
if [ ! -d "$IDEA_HOME" ]; then
    echo "⚠️  警告: 未找到 IntelliJ IDEA 安装目录，尝试常见路径..."

    # 尝试常见的 IntelliJ IDEA 安装路径
    POSSIBLE_PATHS=(
        "/Applications/IntelliJ IDEA.app/Contents"
        "/Applications/IntelliJ IDEA CE.app/Contents"
        "/Applications/IntelliJ IDEA Community Edition.app/Contents"
        "/opt/idea"
        "/usr/local/idea"
        "/usr/local/jetbrains/idea"
        "$HOME/Applications/IntelliJ IDEA.app/Contents"
        "$HOME/.local/share/JetBrains/IntelliJ IDEA"
        "/snap/intellij-idea-ultimate/current"
        "/snap/intellij-idea-community/current"
    )

    for path in "${POSSIBLE_PATHS[@]}"; do
        if [ -d "$path" ]; then
            IDEA_HOME="$path"
            echo "✅ 找到 IntelliJ IDEA: $IDEA_HOME"
            break
        fi
    done

    if [ ! -d "$IDEA_HOME" ]; then
        echo "⚠️  警告: 未找到 IntelliJ IDEA 安装目录，使用基本类路径"
        echo "这可能导致编译错误，但会尝试继续构建..."
        IDEA_HOME=""
    fi
fi

# 检查和下载JSON库
JSON_JAR="json-20240303.jar"
if [ ! -f "$JSON_JAR" ]; then
    echo "📦 下载JSON库..."
    curl -L -o "$JSON_JAR" https://repo1.maven.org/maven2/org/json/json/20240303/json-20240303.jar
    if [ $? -ne 0 ]; then
        echo "❌ JSON库下载失败"
        exit 1
    fi
    echo "✅ JSON库下载成功"
fi

# 构建类路径（包含JSON库）
if [ -n "$IDEA_HOME" ] && [ -d "$IDEA_HOME/lib" ]; then
    CLASSPATH=".:$IDEA_HOME/lib/*:$JSON_JAR"
    if [ -d "$IDEA_HOME/plugins/platform-api/lib" ]; then
        CLASSPATH="$CLASSPATH:$IDEA_HOME/plugins/platform-api/lib/*"
    fi
    echo "✅ 使用 IntelliJ IDEA 类路径: $IDEA_HOME"
else
    CLASSPATH=".:$JSON_JAR"
    echo "⚠️  使用基本类路径，可能导致编译警告"
fi
echo "📦 JSON库已添加到类路径: $JSON_JAR"

# 编译 Java 源文件
echo "🔨 编译 Java 源文件..."

# 按依赖顺序编译核心功能文件
COMPILE_ORDER=(
    # 第一层：基础工具类
    "src/main/java/com/baaon/SecuritySHA1Util.java"

    # 第二层：核心管理类（修复循环依赖）
    "src/main/java/com/baaon/v2/TrialSessionManager.java"
    "src/main/java/com/baaon/v2/SessionId.java"

    # 第三层：依赖前面类的功能类
    "src/main/java/com/baaon/v2/AuthenticationManager.java"
    "src/main/java/com/baaon/v2/SessionIdReplacer.java"

    # 第四层：配置相关类
    "src/main/java/com/baaon/v2/config/ApiResponse.java"
    "src/main/java/com/baaon/v2/config/DomainConstant.java"
    "src/main/java/com/baaon/v2/config/EmailVerificationApiService.java"
    "src/main/java/com/baaon/v2/config/AugmentConfigPanel.java"
    "src/main/java/com/baaon/v2/config/AugmentConfigurable.java"

    # 第五层：插件入口
    "src/main/java/com/baaon/v2/AugmentSessionReplacerPlugin.java"
    "src/main/java/com/baaon/v2/AugmentStartupActivity.java"
)

# 分层编译，确保依赖关系正确
COMPILED_COUNT=0
FAILED_FILES=()

for file in "${COMPILE_ORDER[@]}"; do
    if [ -f "$file" ]; then
        echo "📝 编译: $(basename "$file")"
        # 创建临时文件来捕获错误输出
        ERROR_FILE=$(mktemp)
        if javac -cp "$CLASSPATH:build/classes" -d build/classes -encoding UTF-8 "$file" 2>"$ERROR_FILE"; then
            COMPILED_COUNT=$((COMPILED_COUNT + 1))
            echo "   ✅ 成功"
        else
            echo "   ❌ 失败"
            # 显示编译错误的前几行
            if [ -s "$ERROR_FILE" ]; then
                echo "   错误详情:"
                head -3 "$ERROR_FILE" | sed 's/^/      /'
            fi
            FAILED_FILES+=("$file")
        fi
        rm -f "$ERROR_FILE"
    else
        echo "⚠️  跳过不存在的文件: $file"
    fi
done

echo "📊 编译结果: $COMPILED_COUNT 个文件成功"
if [ ${#FAILED_FILES[@]} -gt 0 ]; then
    echo "⚠️  编译失败的文件:"
    for file in "${FAILED_FILES[@]}"; do
        echo "   - $file"
    done
fi

# 检查是否有编译成功的文件
if [ $COMPILED_COUNT -eq 0 ]; then
    echo "❌ 错误: 没有文件编译成功"
    exit 1
fi

# 复制资源文件
echo "📋 复制资源文件..."
if [ -d "src/main/resources" ]; then
    cp -r src/main/resources/* build/classes/
    echo "✅ 资源文件复制完成"
fi

# 解压JSON库到构建目录
echo "📦 解压JSON库到构建目录..."
if [ -f "$JSON_JAR" ]; then
    cd build/classes
    jar xf "../../$JSON_JAR"
    # 删除META-INF目录，避免冲突
    rm -rf META-INF/MANIFEST.MF
    rm -rf META-INF/maven
    echo "✅ JSON库解压完成"
    cd ../..
else
    echo "⚠️  JSON库文件不存在，跳过解压"
fi

# 创建 JAR 文件
echo "📦 创建插件 JAR 文件..."
cd build/classes

# 创建 JAR 文件
jar cf ../distributions/augment-assistant-enhanced-2.0.0.jar *

cd ../..

# 验证 JAR 文件
if [ -f "build/distributions/augment-assistant-enhanced-2.0.0.jar" ]; then
    echo "✅ 插件构建成功!"
    echo "📍 插件文件位置: build/distributions/augment-assistant-enhanced-2.0.0.jar"

    # 显示 JAR 文件信息
    echo ""
    echo "📊 插件信息:"
    echo "   文件大小: $(du -h build/distributions/augment-assistant-enhanced-2.0.0.jar | cut -f1)"
    echo "   文件路径: $(pwd)/build/distributions/augment-assistant-enhanced-2.0.0.jar"

    # 验证 JAR 内容
    echo ""
    echo "📋 JAR 文件内容预览:"
    jar tf build/distributions/augment-assistant-enhanced-2.0.0.jar | head -20

    echo ""
    echo "🎉 增强版插件构建完成! 现在可以安装插件了:"
    echo "   1. 打开 IntelliJ IDEA"
    echo "   2. File -> Settings -> Plugins"
    echo "   3. 点击齿轮图标 -> Install Plugin from Disk..."
    echo "   4. 选择: $(pwd)/build/distributions/augment-assistant-enhanced-2.0.0.jar"
    echo "   5. 重启 IntelliJ IDEA"
    echo ""
    
else
    echo "❌ 错误: 插件构建失败"
    exit 1
fi
