#!/bin/bash

# Gradle构建脚本 - 使用Gradle构建Augment Assistant插件
# 支持JSON依赖管理，避免手动编译问题

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_message $BLUE "🚀 开始使用Gradle构建 Augment Assistant 插件..."

# 检查Gradle是否可用
if ! command -v gradle &> /dev/null; then
    if [ -f "./gradlew" ]; then
        print_message $YELLOW "📋 使用项目自带的Gradle Wrapper..."
        GRADLE_CMD="./gradlew"
    else
        print_message $RED "❌ 未找到Gradle或Gradle Wrapper！"
        print_message $YELLOW "请安装Gradle或运行: gradle wrapper"
        exit 1
    fi
else
    print_message $GREEN "✅ 找到系统Gradle"
    GRADLE_CMD="gradle"
fi

# 清理之前的构建
print_message $BLUE "🧹 清理之前的构建..."
$GRADLE_CMD clean

# 检查依赖
print_message $BLUE "📦 检查和下载依赖..."
$GRADLE_CMD dependencies --configuration compileClasspath

# 编译项目
print_message $BLUE "🔨 编译Java源文件..."
if $GRADLE_CMD compileJava; then
    print_message $GREEN "✅ Java编译成功"
else
    print_message $RED "❌ Java编译失败"
    exit 1
fi

# 处理资源文件
print_message $BLUE "📋 处理资源文件..."
if $GRADLE_CMD processResources; then
    print_message $GREEN "✅ 资源处理成功"
else
    print_message $RED "❌ 资源处理失败"
    exit 1
fi

# 构建插件JAR
print_message $BLUE "📦 构建插件JAR文件..."
if $GRADLE_CMD buildPlugin; then
    print_message $GREEN "✅ 插件构建成功"
else
    print_message $RED "❌ 插件构建失败"
    exit 1
fi

# 查找生成的JAR文件
JAR_FILE=$(find build/distributions -name "*.jar" -type f | head -1)

if [ -n "$JAR_FILE" ]; then
    # 获取文件信息
    FILE_SIZE=$(du -h "$JAR_FILE" | cut -f1)
    
    print_message $GREEN "🎉 增强版插件构建完成!"
    print_message $BLUE "📍 插件文件位置: $JAR_FILE"
    print_message $BLUE "📊 插件信息:"
    print_message $BLUE "   文件大小: $FILE_SIZE"
    print_message $BLUE "   文件路径: $(realpath "$JAR_FILE")"
    
    # 显示JAR文件内容预览
    print_message $BLUE "📋 JAR 文件内容预览:"
    jar tf "$JAR_FILE" | head -20
    
    print_message $GREEN "🎉 Gradle构建完成! 现在可以安装插件了:"
    print_message $BLUE "   1. 打开 IntelliJ IDEA"
    print_message $BLUE "   2. File -> Settings -> Plugins"
    print_message $BLUE "   3. 点击齿轮图标 -> Install Plugin from Disk..."
    print_message $BLUE "   4. 选择: $(realpath "$JAR_FILE")"
    print_message $BLUE "   5. 重启 IntelliJ IDEA"
    
    print_message $GREEN "📋 增强版特性:"
    print_message $BLUE "   - 🆔 新的插件ID: com.baaon.augment_assistant_enhanced"
    print_message $BLUE "   - 📛 新的插件名称: Augment Assistant Enhanced"
    print_message $BLUE "   - 🔧 优化的正式验证逻辑（任意验证码有效）"
    print_message $BLUE "   - 📦 使用Gradle依赖管理JSON库"
    print_message $BLUE "   - 🔄 与原版本可以共存，不会覆盖原插件"
    print_message $BLUE "   - ✅ 完整的邮箱验证功能"
    
else
    print_message $RED "❌ 未找到生成的JAR文件"
    exit 1
fi
